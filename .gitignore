# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/


**/node_modules
**/out
notes.txt
cached_embeddings.pkl
.ruff_cache
codeql

.DS_Store
.test
.tiktoken_cache


# IntelliJ Plugin
**/**/.gradle
**/**/.qodana
**/**/build

continue_server.build
continue_server.dist

Icon
Icon?

.continuerc.json
.aimi_rc.json
.aider*

notes.md

manual-testing-sandbox/.idea/**
manual-testing-sandbox/.continue/**
extensions/intellij/.idea/**

**/.idea/workspace.xml
**/.idea/usage.statistics.xml
**/.idea/shelf/
**/.idea/inspectionProfiles/Project_Default.xml
**/.idea/php.xml
**/.idea/AndroidProjectSystem.xml
**/.idea/AugmentWebviewStateStore.xml
**/.idea/dataSources.xml
**/.idea/dataSources.local.xml
**/.idea/sqldialects.xml
**/.idea/git_toolbox_prj.xml
**/.idea/jsLibraryMappings.xml


extensions/intellij/bin
extensions/.aimi-debug/

*.vsix

# intellij module library files
*.iml

.continuerules
**/.continue/assistants/
/.idea/material_theme_project_new.xml
keys
