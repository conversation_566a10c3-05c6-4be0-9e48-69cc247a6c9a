import { IDE } from "../..";
import {
  AutocompleteCodeSnippet,
  AutocompleteSnippetType,
} from "../snippets/types";
import { HelperVars } from "../util/HelperVars";

import { ImportDefinitionsService } from "./ImportDefinitionsService";
import { getSymbolsForSnippet } from "./ranking";
import { RootPathContextService } from "./root-path-context/RootPathContextService";
import { ConfigHandler } from "../../config/ConfigHandler";
import { fetchwithRequestOptions } from "@continuedev/fetch";
import {
  DEFAULT_N_FINAL,
  retrieveContextItemsFromEmbeddings,
} from "../../context/retrieval/retrieval";

export class ContextRetrievalService {
  private readonly importDefinitionsService: ImportDefinitionsService;
  private readonly rootPathContextService: RootPathContextService;

  constructor(
    private readonly ide: IDE,
    private readonly configHandler: ConfigHandler,
  ) {
    this.importDefinitionsService = new ImportDefinitionsService(this.ide);
    this.rootPathContextService = new RootPathContextService(
      this.importDefinitionsService,
      this.ide,
    );
  }

  public async getSnippetsFromImportDefinitions(
    helper: HelperVars,
  ): Promise<AutocompleteCodeSnippet[]> {
    if (helper.options.useImports === false) {
      return [];
    }

    const importSnippets: AutocompleteCodeSnippet[] = [];
    const fileInfo = this.importDefinitionsService.get(helper.filepath);
    if (fileInfo) {
      const { imports } = fileInfo;
      // Look for imports of any symbols around the current range
      const textAroundCursor =
        helper.fullPrefix.split("\n").slice(-5).join("\n") +
        helper.fullSuffix.split("\n").slice(0, 3).join("\n");
      const symbols = Array.from(getSymbolsForSnippet(textAroundCursor)).filter(
        (symbol) => !helper.lang.topLevelKeywords.includes(symbol),
      );
      for (const symbol of symbols) {
        const rifs = imports[symbol];
        if (Array.isArray(rifs)) {
          const snippets: AutocompleteCodeSnippet[] = rifs.map((rif) => {
            return {
              filepath: rif.filepath,
              content: rif.contents,
              type: AutocompleteSnippetType.Code,
            };
          });

          importSnippets.push(...snippets);
        }
      }
    }

    return importSnippets;
  }

  public async getRootPathSnippets(
    helper: HelperVars,
  ): Promise<AutocompleteCodeSnippet[]> {
    if (!helper.treePath) {
      return [];
    }

    return this.rootPathContextService.getContextForPath(
      helper.filepath,
      helper.treePath,
    );
  }

  // 召回 相似的代码
  public async retrievalCodeSnippets(
    helper: HelperVars,
  ): Promise<AutocompleteCodeSnippet[]> {
    const { config } = await this.configHandler.reloadConfig();
    if (!config) {
      throw new Error("config is null");
    }
    const providers = config.contextProviders;
    const codebaseProvider = providers.find(
      (item) => item.description.title == "codebase",
    );
    if (!codebaseProvider) return [];

    // 从 helper.fileLines 取  前后 count 行代码
    const count = 2;
    const startLine = Math.max(helper.input.pos.line - count, 0);
    const endLine = Math.min(
      helper.input.pos.line + count,
      helper.fileLines.length,
    );
    const keyWords = ["(", ")", "[", "]", "{", "}"];
    const codeLines = helper.fileLines
      .slice(startLine, endLine + 1)
      .filter((line) => {
        const trimmed = line.trim();
        return trimmed.length > 0 && !keyWords.includes(trimmed);
      })
      .join("\n");
    const codeLine = helper.fileLines[helper.pos.line].trim();

    try {
      const items = await retrieveContextItemsFromEmbeddings(
        {
          config: config,
          fullInput: codeLine.length > 0 ? codeLine : codeLines,
          embeddingsProvider: config.selectedModelByRole.embed,
          reranker: config.selectedModelByRole.rerank,
          llm: config.selectedModelByRole.chat!,
          ide: this.ide,
          selectedCode: helper.input.recentlyEditedRanges,
          fetch: function (url: string | URL, init?: any): Promise<any> {
            return fetchwithRequestOptions(url, init, config.requestOptions);
          },
        },
        {
          nFinal: DEFAULT_N_FINAL,
        },
        undefined,
      );

      return items.map((contextItem) => {
        return {
          filepath: contextItem.uri?.value || "",
          content: contextItem.content,
          type: AutocompleteSnippetType.Code,
        };
      });
    } catch (e) {
      console.log(`retrievalCodeSnippets error, ${e}`);
      return [];
    }
  }
}
