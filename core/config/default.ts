import { ConfigYaml } from "@continuedev/config-yaml";

export const defaultContextProvidersVsCode: NonNullable<
  ConfigYaml["context"]
>[number][] = [
  { provider: "code" },
  { provider: "docs" },
  { provider: "diff" },
  { provider: "terminal" },
  { provider: "problems" },
  { provider: "folder" },
  { provider: "codebase" },
];

export const defaultContextProvidersJetBrains: NonNullable<
  ConfigYaml["context"]
>[number][] = [
  { provider: "diff" },
  { provider: "folder" },
  { provider: "codebase" },
];

export const defaultConfig: ConfigYaml = {
  name: "Local Assistant",
  version: "1.0.0",
  schema: "v1",
  models: [
    {
      name: "AIMI",
      provider: "openai",
      model: "AIMI-Chat-32B",
      apiKey: "111",
      apiBase: "https://pre-mtl4.alibaba-inc.com/ai/api/v2/stream",
      roles: ["edit", "chat"],
    },
    {
      name: "IDE_APPLY",
      provider: "openai",
      model: "IDE_APPLY",
      apiKey: "111",
      apiBase: "https://pre-mtl4.alibaba-inc.com/ai/api/v2/stream",
      roles: ["apply"],
    },
    {
      name: "mtl-code",
      provider: "openai",
      model: "MTL-coderqwen",
      apiKey: "111",
      apiBase: "https://pre-mtl4.alibaba-inc.com/ai/api/v2/stream",
      roles: ["autocomplete"],
    },
    {
      name: "IDE_EMBED",
      provider: "openai",
      model: "IDE_EMBEDING",
      apiKey: "8e25c3cf1b8f06af9f33bcb8eb4c06c2",
      apiBase: "https://pre-mtl4.alibaba-inc.com/ai/api/v1/ide",
      roles: ["embed"],
      embedOptions: {
        maxChunkSize: 256,
        maxBatchSize: 5,
        maxEmbeddingBatchSize: 5,
      },
    },
    {
      name: "voyage",
      provider: "voyage",
      model: "IDE_RERANKER",
      apiBase: "https://pre-mtl4.alibaba-inc.com/ai/api/v1/ide",
      roles: ["rerank"],
    },
  ],
  context: defaultContextProvidersVsCode,
};

export const defaultConfigJetBrains: ConfigYaml = {
  name: "Local Assistant",
  version: "1.0.0",
  schema: "v1",
  models: defaultConfig.models,
  context: defaultContextProvidersJetBrains,
};
