---
title: 安装
description: 开发者的开源 AI 代码助手
keywords: [安装, 开始, vs code, jetbrains]
---

## VS Code

1. 点击 [Visual Studio 市场中 Continue 扩展页面](https://marketplace.visualstudio.com/items?itemName=Continue.continue) 的 `安装` 按钮

2. 这会打开 VS Code 中的 Continue 扩展页面，你需要再次点击 `安装`

3. Continue 标志会出现在左侧边栏中。为了获得更好的体验，可以把 Continue 移动到右侧边栏

![move-to-right-sidebar](/img/move-to-right-sidebar.gif)

:::info[故障排除]
如果你有任何问题，查看 [故障排除指南](troubleshooting.md) 或者在 [我们的 Discord](https://discord.gg/NWtdYexhMs) 寻求帮助
:::

## JetBrains

1. 打开你的 JetBrains IDE 并使用 `cmd/ctrl + ,` 打开 **设置**

2. 在侧边栏中选择 **插件** 并在市场中查找 "AIMI"

3. 点击 `安装`， 随后 Continue 标志会出现在右侧边栏

![jetbrains-getting-started.png](/img/jetbrains-getting-started.png)

:::info[故障排除]
如果你有任何问题，查看 [故障排除指南](troubleshooting.md) 或者在 [我们的 Discord](https://discord.com/invite/EfJEfdFnDQ) 寻求帮助
:::
