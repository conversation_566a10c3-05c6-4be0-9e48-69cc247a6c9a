{"name": "Handlebars", "repository": {"html_tags": {"patterns": [{"begin": "(<)([a-zA-Z0-9:-]+)(?=[^>]*></\\2>)", "beginCaptures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.html"}}, "end": "(>(<)/)(\\2)(>)", "endCaptures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "meta.scope.between-tag-pair.html"}, "3": {"name": "entity.name.tag.html"}, "4": {"name": "punctuation.definition.tag.html"}}, "name": "meta.tag.any.html", "patterns": [{"include": "#tag-stuff"}]}, {"begin": "(<\\?)(xml)", "captures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.xml.html"}}, "end": "(\\?>)", "name": "meta.tag.preprocessor.xml.html", "patterns": [{"include": "#tag_generic_attribute"}, {"include": "#string"}]}, {"begin": "<!--", "captures": {"0": {"name": "punctuation.definition.comment.html"}}, "end": "--\\s*>", "name": "comment.block.html", "patterns": [{"match": "--", "name": "invalid.illegal.bad-comments-or-CDATA.html"}]}, {"begin": "<!", "captures": {"0": {"name": "punctuation.definition.tag.html"}}, "end": ">", "name": "meta.tag.sgml.html", "patterns": [{"begin": "(DOCTYPE|doctype)", "captures": {"1": {"name": "entity.name.tag.doctype.html"}}, "end": "(?=>)", "name": "meta.tag.sgml.doctype.html", "patterns": [{"match": "\"[^\">]*\"", "name": "string.quoted.double.doctype.identifiers-and-DTDs.html"}]}, {"begin": "\\[CDATA\\[", "end": "]](?=>)", "name": "constant.other.inline-data.html"}, {"match": "(\\s*)(?!--|>)\\S(\\s*)", "name": "invalid.illegal.bad-comments-or-CDATA.html"}]}, {"begin": "(?:^\\s+)?(<)((?i:style))\\b(?![^>]*/>)", "captures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.style.html"}, "3": {"name": "punctuation.definition.tag.html"}}, "end": "(</)((?i:style))(>)(?:\\s*\\n)?", "name": "source.css.embedded.html", "patterns": [{"include": "#tag-stuff"}, {"begin": "(>)", "beginCaptures": {"1": {"name": "punctuation.definition.tag.html"}}, "end": "(?=</(?i:style))", "patterns": [{"include": "source.css"}]}]}, {"begin": "(?:^\\s+)?(<)((?i:script))\\b(?![^>]*/>)", "beginCaptures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.script.html"}}, "end": "(?<=</(script|SCRIPT))(>)(?:\\s*\\n)?", "endCaptures": {"2": {"name": "punctuation.definition.tag.html"}}, "name": "source.js.embedded.html", "patterns": [{"include": "#tag-stuff"}, {"begin": "(?<!</(?:script|SCRIPT))(>)", "captures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.script.html"}}, "end": "(</)((?i:script))", "patterns": [{"captures": {"1": {"name": "punctuation.definition.comment.js"}}, "match": "(//).*?((?=</script)|$\\n?)", "name": "comment.line.double-slash.js"}, {"begin": "/\\*", "captures": {"0": {"name": "punctuation.definition.comment.js"}}, "end": "\\*/|(?=</script)", "name": "comment.block.js"}, {"include": "source.js"}]}]}, {"begin": "(</?)((?i:body|head|html)\\b)", "captures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.structure.any.html"}}, "end": "(>)", "name": "meta.tag.structure.any.html", "patterns": [{"include": "#tag-stuff"}]}, {"begin": "(</?)((?i:address|blockquote|dd|div|header|section|footer|aside|nav|dl|dt|fieldset|form|frame|frameset|h1|h2|h3|h4|h5|h6|iframe|noframes|object|ol|p|ul|applet|center|dir|hr|menu|pre)\\b)", "captures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.block.any.html"}}, "end": "(>)", "name": "meta.tag.block.any.html", "patterns": [{"include": "#tag-stuff"}]}, {"begin": "(</?)((?i:a|abbr|acronym|area|b|base|basefont|bdo|big|br|button|caption|cite|code|col|colgroup|del|dfn|em|font|head|html|i|img|input|ins|isindex|kbd|label|legend|li|link|map|meta|noscript|optgroup|option|param|q|s|samp|script|select|small|span|strike|strong|style|sub|sup|table|tbody|td|textarea|tfoot|th|thead|title|tr|tt|u|var)\\b)", "captures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.inline.any.html"}}, "end": "((?: ?/)?>)", "name": "meta.tag.inline.any.html", "patterns": [{"include": "#tag-stuff"}]}, {"begin": "(</?)([a-zA-Z0-9:-]+)", "captures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.other.html"}}, "end": "(>)", "name": "meta.tag.other.html", "patterns": [{"include": "#tag-stuff"}]}, {"begin": "(</?)([a-zA-Z0-9{}:-]+)", "captures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.tokenised.html"}}, "end": "(>)", "name": "meta.tag.tokenised.html", "patterns": [{"include": "#tag-stuff"}]}, {"include": "#entities"}, {"match": "<>", "name": "invalid.illegal.incomplete.html"}, {"match": "<", "name": "invalid.illegal.bad-angle-bracket.html"}]}, "entities": {"patterns": [{"captures": {"1": {"name": "punctuation.definition.entity.html"}, "3": {"name": "punctuation.definition.entity.html"}}, "name": "constant.character.entity.html", "match": "(&)([a-zA-Z0-9]+|#[0-9]+|#x[0-9a-fA-F]+)(;)"}, {"name": "invalid.illegal.bad-ampersand.html", "match": "&"}]}, "end_block": {"begin": "(\\{\\{~?/)([a-zA-Z0-9_\\.-]+)\\s*", "end": "(~?\\}\\})", "name": "meta.function.block.end.handlebars", "endCaptures": {"1": {"name": "support.constant.handlebars"}}, "beginCaptures": {"1": {"name": "support.constant.handlebars"}, "2": {"name": "support.constant.handlebars"}}, "patterns": []}, "yfm": {"patterns": [{"patterns": [{"include": "source.yaml"}], "begin": "(?<!\\s)---\\n$", "end": "^---\\s", "name": "markup.raw.yaml.front-matter"}]}, "comments": {"patterns": [{"patterns": [{"name": "keyword.annotation.handlebars", "match": "@\\w*"}, {"include": "#comments"}], "begin": "\\{\\{!", "end": "\\}\\}", "name": "comment.block.handlebars"}, {"captures": {"0": {"name": "punctuation.definition.comment.html"}}, "begin": "<!--", "end": "-{2,3}\\s*>", "name": "comment.block.html", "patterns": [{"name": "invalid.illegal.bad-comments-or-CDATA.html", "match": "--"}]}]}, "block_comments": {"patterns": [{"patterns": [{"name": "keyword.annotation.handlebars", "match": "@\\w*"}, {"include": "#comments"}], "begin": "\\{\\{!--", "end": "--\\}\\}", "name": "comment.block.handlebars"}, {"captures": {"0": {"name": "punctuation.definition.comment.html"}}, "begin": "<!--", "end": "-{2,3}\\s*>", "name": "comment.block.html", "patterns": [{"name": "invalid.illegal.bad-comments-or-CDATA.html", "match": "--"}]}]}, "block_helper": {"begin": "(\\{\\{~?\\#)([-a-zA-Z0-9_\\./]+)\\s?(@?[-a-zA-Z0-9_\\./]+)*\\s?(@?[-a-zA-Z0-9_\\./]+)*\\s?(@?[-a-zA-Z0-9_\\./]+)*", "end": "(~?\\}\\})", "name": "meta.function.block.start.handlebars", "endCaptures": {"1": {"name": "support.constant.handlebars"}}, "beginCaptures": {"1": {"name": "support.constant.handlebars"}, "2": {"name": "support.constant.handlebars"}, "3": {"name": "variable.parameter.handlebars"}, "4": {"name": "support.constant.handlebars"}, "5": {"name": "variable.parameter.handlebars"}, "6": {"name": "support.constant.handlebars"}}, "patterns": [{"include": "#string"}, {"include": "#handlebars_attribute"}]}, "string-single-quoted": {"begin": "'", "end": "'", "name": "string.quoted.single.handlebars", "endCaptures": {"0": {"name": "punctuation.definition.string.end.html"}}, "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.html"}}, "patterns": [{"include": "#escaped-single-quote"}, {"include": "#block_comments"}, {"include": "#comments"}, {"include": "#block_helper"}, {"include": "#else_token"}, {"include": "#end_block"}, {"include": "#partial_and_var"}]}, "string": {"patterns": [{"include": "#string-single-quoted"}, {"include": "#string-double-quoted"}]}, "escaped-single-quote": {"name": "constant.character.escape.js", "match": "\\\\'"}, "escaped-double-quote": {"name": "constant.character.escape.js", "match": "\\\\\""}, "partial_and_var": {"begin": "(\\{\\{~?\\{*(>|!<)*)\\s*(@?[-a-zA-Z0-9_\\./]+)*", "end": "(~?\\}\\}\\}*)", "name": "meta.function.inline.other.handlebars", "beginCaptures": {"1": {"name": "support.constant.handlebars"}, "3": {"name": "variable.parameter.handlebars"}}, "endCaptures": {"1": {"name": "support.constant.handlebars"}}, "patterns": [{"include": "#string"}, {"include": "#handlebars_attribute"}]}, "handlebars_attribute_name": {"begin": "\\b([-a-zA-Z0-9_\\.]+)\\b=", "captures": {"1": {"name": "variable.parameter.handlebars"}}, "end": "(?='|\"|)", "name": "entity.other.attribute-name.handlebars"}, "handlebars_attribute_value": {"begin": "([-a-zA-Z0-9_\\./]+)\\b\\s*", "captures": {"1": {"name": "variable.parameter.handlebars"}}, "end": "('|\"|)", "name": "entity.other.attribute-value.handlebars", "patterns": [{"include": "#string"}]}, "handlebars_attribute": {"patterns": [{"include": "#handlebars_attribute_name"}, {"include": "#handlebars_attribute_value"}]}, "extends": {"patterns": [{"end": "(\\}\\})", "begin": "(\\{\\{!<)\\s([-a-zA-Z0-9_\\./]+)", "beginCaptures": {"1": {"name": "support.function.handlebars"}, "2": {"name": "support.class.handlebars"}}, "endCaptures": {"1": {"name": "support.function.handlebars"}}, "name": "meta.preprocessor.handlebars"}]}, "else_token": {"begin": "(\\{\\{~?else)(@?\\s(if)\\s([-a-zA-Z0-9_\\./]+))?", "end": "(~?\\}\\}\\}*)", "name": "meta.function.inline.else.handlebars", "beginCaptures": {"1": {"name": "support.constant.handlebars"}, "3": {"name": "support.constant.handlebars"}, "4": {"name": "variable.parameter.handlebars"}}, "endCaptures": {"1": {"name": "support.constant.handlebars"}}}, "string-double-quoted": {"begin": "\"", "end": "\"", "name": "string.quoted.double.handlebars", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.html"}}, "endCaptures": {"0": {"name": "punctuation.definition.string.end.html"}}, "patterns": [{"include": "#escaped-double-quote"}, {"include": "#block_comments"}, {"include": "#comments"}, {"include": "#block_helper"}, {"include": "#else_token"}, {"include": "#end_block"}, {"include": "#partial_and_var"}]}, "inline_script": {"begin": "(?:^\\s+)?(<)((?i:script))\\b(?:.*(type)=([\"'](?:text/x-handlebars-template|text/x-handlebars|text/template|x-tmpl-handlebars)[\"']))(?![^>]*/>)", "beginCaptures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.script.html"}, "3": {"name": "entity.other.attribute-name.html"}, "4": {"name": "string.quoted.double.html"}}, "end": "(?<=</(script|SCRIPT))(>)(?:\\s*\\n)?", "endCaptures": {"2": {"name": "punctuation.definition.tag.html"}}, "name": "source.handlebars.embedded.html", "patterns": [{"include": "#tag-stuff"}, {"begin": "(?<!</(?:script|SCRIPT))(>)", "captures": {"1": {"name": "punctuation.definition.tag.html"}, "2": {"name": "entity.name.tag.script.html"}}, "end": "(</)((?i:script))", "patterns": [{"include": "#block_comments"}, {"include": "#comments"}, {"include": "#block_helper"}, {"include": "#end_block"}, {"include": "#else_token"}, {"include": "#partial_and_var"}, {"include": "#html_tags"}, {"include": "text.html.basic"}]}]}, "tag_generic_attribute": {"begin": "\\b([a-zA-Z0-9_-]+)\\b\\s*(=)", "captures": {"1": {"name": "entity.other.attribute-name.generic.html"}, "2": {"name": "punctuation.separator.key-value.html"}}, "patterns": [{"include": "#string"}], "name": "entity.other.attribute-name.html", "end": "(?<='|\"|)"}, "tag_id_attribute": {"begin": "\\b(id)\\b\\s*(=)", "captures": {"1": {"name": "entity.other.attribute-name.id.html"}, "2": {"name": "punctuation.separator.key-value.html"}}, "end": "(?<='|\"|)", "name": "meta.attribute-with-value.id.html", "patterns": [{"include": "#string"}]}, "tag-stuff": {"patterns": [{"include": "#tag_id_attribute"}, {"include": "#tag_generic_attribute"}, {"include": "#string"}, {"include": "#block_comments"}, {"include": "#comments"}, {"include": "#block_helper"}, {"include": "#end_block"}, {"include": "#else_token"}, {"include": "#partial_and_var"}]}}, "scopeName": "text.html.handlebars", "patterns": [{"include": "#yfm"}, {"include": "#extends"}, {"include": "#block_comments"}, {"include": "#comments"}, {"include": "#block_helper"}, {"include": "#end_block"}, {"include": "#else_token"}, {"include": "#partial_and_var"}, {"include": "#inline_script"}, {"include": "#html_tags"}, {"include": "text.html.basic"}], "fileTypes": ["handlebars", "handlebars.html", "hbr", "hbrs", "hbs", "hdbs", "hjs", "mu", "mustache", "rac", "stache", "template", "tmpl"], "uuid": "70E91676-DE0A-4266-A2B9-3AD2E535E484", "version": "https://github.com/daaain/Handlebars/commit/bdaec7aaf5eaac45d1878096110d1db7975fb100"}