{"fileTypes": ["sh", "bash", "zsh", "bashrc", "bash_profile", "bash_login", "profile", "bash_logout", ".textmate_init"], "firstLineMatch": "^#!.*\\b(bash|zsh|sh|tcsh)|^#.*-\\*-.*\\bshell-script\\b.*-\\*-", "keyEquivalent": "^~S", "name": "<PERSON> Script (Bash)", "patterns": [{"include": "#comment"}, {"include": "#pipeline"}, {"include": "#list"}, {"include": "#compound-command"}, {"include": "#loop"}, {"include": "#string"}, {"include": "#function-definition"}, {"include": "#variable"}, {"include": "#interpolation"}, {"include": "#heredoc"}, {"include": "#herestring"}, {"include": "#redirection"}, {"include": "#pathname"}, {"include": "#keyword"}, {"include": "#support"}], "repository": {"case-clause": {"patterns": [{"begin": "(?=\\S)", "end": ";;", "endCaptures": {"0": {"name": "punctuation.terminator.case-clause.shell"}}, "name": "meta.scope.case-clause.shell", "patterns": [{"begin": "(\\(|(?=\\S))", "captures": {"0": {"name": "punctuation.definition.case-pattern.shell"}}, "end": "\\)", "name": "meta.scope.case-pattern.shell", "patterns": [{"match": "\\|", "name": "punctuation.separator.pipe-sign.shell"}, {"include": "#string"}, {"include": "#variable"}, {"include": "#interpolation"}, {"include": "#pathname"}]}, {"begin": "(?<=\\))", "end": "(?=;;)", "name": "meta.scope.case-clause-body.shell", "patterns": [{"include": "$self"}]}]}]}, "comment": {"begin": "(^[ \\t]+)?(?<!\\S)(?=#)(?!#\\{)", "beginCaptures": {"1": {"name": "punctuation.whitespace.comment.leading.shell"}}, "end": "(?!\\G)", "patterns": [{"begin": "^(#!)", "beginCaptures": {"1": {"name": "punctuation.definition.comment.line.shebang.shell"}}, "end": "\\n", "name": "comment.line.shebang.shell"}, {"begin": "#", "beginCaptures": {"0": {"name": "punctuation.definition.comment.shell"}}, "end": "\\n", "name": "comment.line.number-sign.shell"}]}, "compound-command": {"patterns": [{"begin": "(\\[{2})", "captures": {"1": {"name": "punctuation.definition.logical-expression.shell"}}, "end": "(\\]{2})", "name": "meta.scope.logical-expression.shell", "patterns": [{"include": "#logical-expression"}, {"include": "$self"}]}, {"begin": "(\\({2})", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.shell"}}, "end": "(\\){2})", "endCaptures": {"0": {"name": "punctuation.definition.string.end.shell"}}, "name": "string.other.math.shell", "patterns": [{"include": "#math"}]}, {"begin": "(\\()", "captures": {"1": {"name": "punctuation.definition.subshell.shell"}}, "end": "(\\))", "name": "meta.scope.subshell.shell", "patterns": [{"include": "$self"}]}, {"begin": "(?<=\\s|^)(\\{)(?=\\s|$)", "captures": {"1": {"name": "punctuation.definition.group.shell"}}, "end": "(?<=^|;)\\s*(\\})", "name": "meta.scope.group.shell", "patterns": [{"include": "$self"}]}]}, "function-definition": {"patterns": [{"begin": "(?<=^|;|&|\\s)(function)\\s+([^\\s<>;'\"\\\\|$&()]+)(?:\\s*(\\(\\)))?", "beginCaptures": {"1": {"name": "storage.type.function.shell"}, "2": {"name": "entity.name.function.shell"}, "3": {"name": "punctuation.definition.arguments.shell"}}, "end": ";|&|$", "endCaptures": {"0": {"name": "punctuation.definition.function.shell"}}, "name": "meta.function.shell", "patterns": [{"include": "$self"}]}, {"begin": "(?<=^|;|&|\\s)([^\\s<>;'\"\\\\|$&()]+)\\s*(\\(\\))", "beginCaptures": {"1": {"name": "entity.name.function.shell"}, "2": {"name": "punctuation.definition.arguments.shell"}}, "end": ";|&|$", "endCaptures": {"0": {"name": "punctuation.definition.function.shell"}}, "name": "meta.function.shell", "patterns": [{"include": "$self"}]}]}, "heredoc": {"patterns": [{"begin": "(<<)-\\s*(\"|'|)(RUBY)\\2", "beginCaptures": {"1": {"name": "keyword.operator.heredoc.shell"}, "3": {"name": "keyword.control.heredoc-token.shell"}}, "captures": {"0": {"name": "punctuation.definition.string.shell"}}, "contentName": "source.ruby.embedded.shell", "end": "^\\t*(RUBY)(?=\\s|;|&|$)", "endCaptures": {"1": {"name": "keyword.control.heredoc-token.shell"}}, "name": "string.unquoted.heredoc.no-indent.ruby.shell", "patterns": [{"include": "source.ruby"}]}, {"begin": "(<<)\\s*(\"|'|)(RUBY)\\2", "beginCaptures": {"1": {"name": "keyword.operator.heredoc.shell"}, "3": {"name": "keyword.control.heredoc-token.shell"}}, "captures": {"0": {"name": "punctuation.definition.string.shell"}}, "contentName": "source.ruby.embedded.shell", "end": "^(RUBY)(?=\\s|;|&|$)", "endCaptures": {"1": {"name": "keyword.control.heredoc-token.shell"}}, "name": "string.unquoted.heredoc.ruby.shell", "patterns": [{"include": "source.ruby"}]}, {"begin": "(<<)-\\s*(\"|'|)(PYTHON)\\2", "beginCaptures": {"1": {"name": "keyword.operator.heredoc.shell"}, "3": {"name": "keyword.control.heredoc-token.shell"}}, "captures": {"0": {"name": "punctuation.definition.string.shell"}}, "contentName": "source.python.embedded.shell", "end": "^\\t*(PYTHON)(?=\\s|;|&|$)", "endCaptures": {"1": {"name": "keyword.control.heredoc-token.shell"}}, "name": "string.unquoted.heredoc.no-indent.python.shell", "patterns": [{"include": "source.python"}]}, {"begin": "(<<)\\s*(\"|'|)(PYTHON)\\2", "beginCaptures": {"1": {"name": "keyword.operator.heredoc.shell"}, "3": {"name": "keyword.control.heredoc-token.shell"}}, "captures": {"0": {"name": "punctuation.definition.string.shell"}}, "contentName": "source.python.embedded.shell", "end": "^(PYTHON)(?=\\s|;|&|$)", "endCaptures": {"1": {"name": "keyword.control.heredoc-token.shell"}}, "name": "string.unquoted.heredoc.python.shell", "patterns": [{"include": "source.python"}]}, {"begin": "(<<)-\\s*(\"|'|)(APPLESCRIPT)\\2", "beginCaptures": {"1": {"name": "keyword.operator.heredoc.shell"}, "3": {"name": "keyword.control.heredoc-token.shell"}}, "captures": {"0": {"name": "punctuation.definition.string.shell"}}, "contentName": "source.applescript.embedded.shell", "end": "^\\t*(APPLESCRIPT)(?=\\s|;|&|$)", "endCaptures": {"1": {"name": "keyword.control.heredoc-token.shell"}}, "name": "string.unquoted.heredoc.no-indent.applescript.shell", "patterns": [{"include": "source.applescript"}]}, {"begin": "(<<)\\s*(\"|'|)(APPLESCRIPT)\\2", "beginCaptures": {"1": {"name": "keyword.operator.heredoc.shell"}, "3": {"name": "keyword.control.heredoc-token.shell"}}, "captures": {"0": {"name": "punctuation.definition.string.shell"}}, "contentName": "source.applescript.embedded.shell", "end": "^(APPLESCRIPT)(?=\\s|;|&|$)", "endCaptures": {"1": {"name": "keyword.control.heredoc-token.shell"}}, "name": "string.unquoted.heredoc.applescript.shell", "patterns": [{"include": "source.applescript"}]}, {"begin": "(<<)-\\s*(\"|'|)(HTML)\\2", "beginCaptures": {"1": {"name": "keyword.operator.heredoc.shell"}, "3": {"name": "keyword.control.heredoc-token.shell"}}, "captures": {"0": {"name": "punctuation.definition.string.shell"}}, "contentName": "text.html.embedded.shell", "end": "^\\t*(HTML)(?=\\s|;|&|$)", "endCaptures": {"1": {"name": "keyword.control.heredoc-token.shell"}}, "name": "string.unquoted.heredoc.no-indent.html.shell", "patterns": [{"include": "text.html.basic"}]}, {"begin": "(<<)\\s*(\"|'|)(HTML)\\2", "beginCaptures": {"1": {"name": "keyword.operator.heredoc.shell"}, "3": {"name": "keyword.control.heredoc-token.shell"}}, "captures": {"0": {"name": "punctuation.definition.string.shell"}}, "contentName": "text.html.embedded.shell", "end": "^(HTML)(?=\\s|;|&|$)", "endCaptures": {"1": {"name": "keyword.control.heredoc-token.shell"}}, "name": "string.unquoted.heredoc.html.shell", "patterns": [{"include": "text.html.basic"}]}, {"begin": "(<<)-\\s*(\"|'|)(MARKDOWN)\\2", "beginCaptures": {"1": {"name": "keyword.operator.heredoc.shell"}, "3": {"name": "keyword.control.heredoc-token.shell"}}, "captures": {"0": {"name": "punctuation.definition.string.shell"}}, "contentName": "text.html.markdown.embedded.shell", "end": "^\\t*(MARKDOWN)(?=\\s|;|&|$)", "endCaptures": {"1": {"name": "keyword.control.heredoc-token.shell"}}, "name": "string.unquoted.heredoc.no-indent.markdown.shell", "patterns": [{"include": "text.html.markdown"}]}, {"begin": "(<<)\\s*(\"|'|)(MARKDOWN)\\2", "beginCaptures": {"1": {"name": "keyword.operator.heredoc.shell"}, "3": {"name": "keyword.control.heredoc-token.shell"}}, "captures": {"0": {"name": "punctuation.definition.string.shell"}}, "contentName": "text.html.markdown.embedded.shell", "end": "^(MARKDOWN)(?=\\s|;|&|$)", "endCaptures": {"1": {"name": "keyword.control.heredoc-token.shell"}}, "name": "string.unquoted.heredoc.markdown.shell", "patterns": [{"include": "text.html.markdown"}]}, {"begin": "(<<)-\\s*(\"|'|)(TEXTILE)\\2", "beginCaptures": {"1": {"name": "keyword.operator.heredoc.shell"}, "3": {"name": "keyword.control.heredoc-token.shell"}}, "captures": {"0": {"name": "punctuation.definition.string.shell"}}, "contentName": "text.html.textile.embedded.shell", "end": "^\\t*(TEXTILE)(?=\\s|;|&|$)", "endCaptures": {"1": {"name": "keyword.control.heredoc-token.shell"}}, "name": "string.unquoted.heredoc.no-indent.textile.shell", "patterns": [{"include": "text.html.textile"}]}, {"begin": "(<<)\\s*(\"|'|)(TEXTILE)\\2", "beginCaptures": {"1": {"name": "keyword.operator.heredoc.shell"}, "3": {"name": "keyword.control.heredoc-token.shell"}}, "captures": {"0": {"name": "punctuation.definition.string.shell"}}, "contentName": "text.html.textile.embedded.shell", "end": "^(TEXTILE)(?=\\s|;|&|$)", "endCaptures": {"1": {"name": "keyword.control.heredoc-token.shell"}}, "name": "string.unquoted.heredoc.textile.shell", "patterns": [{"include": "text.html.textile"}]}, {"begin": "(<<)-\\s*(\"|'|)\\\\?(\\w+)\\2", "beginCaptures": {"1": {"name": "keyword.operator.heredoc.shell"}, "3": {"name": "keyword.control.heredoc-token.shell"}}, "captures": {"0": {"name": "punctuation.definition.string.shell"}}, "end": "^\\t*(\\3)(?=\\s|;|&|$)", "endCaptures": {"1": {"name": "keyword.control.heredoc-token.shell"}}, "name": "string.unquoted.heredoc.no-indent.shell"}, {"begin": "(<<)\\s*(\"|'|)\\\\?(\\w+)\\2", "beginCaptures": {"1": {"name": "keyword.operator.heredoc.shell"}, "3": {"name": "keyword.control.heredoc-token.shell"}}, "captures": {"0": {"name": "punctuation.definition.string.shell"}}, "end": "^(\\3)(?=\\s|;|&|$)", "endCaptures": {"1": {"name": "keyword.control.heredoc-token.shell"}}, "name": "string.unquoted.heredoc.shell"}]}, "herestring": {"patterns": [{"captures": {"1": {"name": "keyword.operator.herestring.shell"}, "2": {"name": "string.quoted.single.herestring.shell"}, "3": {"name": "punctuation.definition.string.begin.shell"}, "4": {"name": "punctuation.definition.string.end.shell"}}, "match": "(<<<)\\s*((')[^']*('))", "name": "meta.herestring.shell"}, {"captures": {"1": {"name": "keyword.operator.herestring.shell"}, "2": {"name": "string.quoted.double.herestring.shell"}, "3": {"name": "punctuation.definition.string.begin.shell"}, "6": {"name": "punctuation.definition.string.end.shell"}}, "match": "(<<<)\\s*((\")(\\\\(\"|\\\\)|[^\"])*(\"))", "name": "meta.herestring.shell"}, {"captures": {"1": {"name": "keyword.operator.herestring.shell"}, "2": {"name": "string.unquoted.herestring.shell"}}, "match": "(<<<)\\s*(([^\\s\\\\]|\\\\.)+)", "name": "meta.herestring.shell"}]}, "interpolation": {"patterns": [{"begin": "\\$\\({2}", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.shell"}}, "end": "\\){2}", "endCaptures": {"0": {"name": "punctuation.definition.string.end.shell"}}, "name": "string.other.math.shell", "patterns": [{"include": "#math"}]}, {"begin": "`", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.shell"}}, "end": "`", "endCaptures": {"0": {"name": "punctuation.definition.string.end.shell"}}, "name": "string.interpolated.backtick.shell", "patterns": [{"match": "\\\\[`\\\\$]", "name": "constant.character.escape.shell"}, {"include": "$self"}]}, {"begin": "\\$\\(", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.shell"}}, "end": "\\)", "endCaptures": {"0": {"name": "punctuation.definition.string.end.shell"}}, "name": "string.interpolated.dollar.shell", "patterns": [{"include": "$self"}]}]}, "keyword": {"patterns": [{"match": "(?<=^|;|&|\\s)(?:if|then|else|elif|fi|for|in|do|done|select|case|continue|esac|while|until|return|coproc)(?=\\s|;|&|$)", "name": "keyword.control.shell"}, {"match": "(?<=^|;|&|\\s)(?:export|declare|typeset|local|readonly)(?=\\s|;|&|$)", "name": "storage.modifier.shell"}]}, "list": {"patterns": [{"match": ";|&&|&|\\|\\|", "name": "keyword.operator.list.shell"}]}, "logical-expression": {"patterns": [{"comment": "do we want a special rule for ( expr )?", "match": "=[=~]?|!=?|<|>|&&|\\|\\|", "name": "keyword.operator.logical.shell"}, {"match": "(?<!\\S)-(nt|ot|ef|eq|ne|l[te]|g[te]|[a-hknoprstuwxzOGLSN])", "name": "keyword.operator.logical.shell"}]}, "loop": {"patterns": [{"begin": "(?<=^|;|&|\\s)(for)\\s+(?=\\({2})", "captures": {"1": {"name": "keyword.control.shell"}}, "end": "(?<=^|;|&|\\s)(done)(?=\\s|;|&|$)", "name": "meta.scope.for-loop.shell", "patterns": [{"include": "$self"}]}, {"begin": "(?<=^|;|&|\\s)(for)\\s+([^\\s\\\\]+)(?=\\s|;|&|$)", "beginCaptures": {"1": {"name": "keyword.control.shell"}, "2": {"name": "variable.other.loop.shell"}}, "end": "(?<=^|;|&|\\s)(done)(?=\\s|;|&|$)", "endCaptures": {"1": {"name": "keyword.control.shell"}}, "name": "meta.scope.for-in-loop.shell", "patterns": [{"include": "$self"}]}, {"begin": "(?<=^|;|&|\\s)(while|until)(?=\\s|;|&|$)", "captures": {"1": {"name": "keyword.control.shell"}}, "end": "(?<=^|;|&|\\s)(done)(?=\\s|;|&|$)", "name": "meta.scope.while-loop.shell", "patterns": [{"include": "$self"}]}, {"begin": "(?<=^|;|&|\\s)(select)\\s+([^\\s\\\\]+)(?=\\s|;|&|$)", "beginCaptures": {"1": {"name": "keyword.control.shell"}, "2": {"name": "variable.other.loop.shell"}}, "end": "(?<=^|;|&|\\s)(done)(?=\\s|;|&|$)", "endCaptures": {"1": {"name": "keyword.control.shell"}}, "name": "meta.scope.select-block.shell", "patterns": [{"include": "$self"}]}, {"begin": "(?<=^|;|&|\\s)(case)(?=\\s|;|&|$)", "captures": {"1": {"name": "keyword.control.shell"}}, "end": "(?<=^|;|&|\\s)(esac)(?=\\s|;|&|$)", "name": "meta.scope.case-block.shell", "patterns": [{"begin": "(?<=^|;|&|\\s)(?:in)(?=\\s|;|&|$)", "beginCaptures": {"1": {"name": "keyword.control.shell"}}, "end": "(?<=^|;|&|\\s)(?=(?:esac)(?:\\s|;|&|$))", "name": "meta.scope.case-body.shell", "patterns": [{"include": "#comment"}, {"include": "#case-clause"}, {"include": "$self"}]}, {"include": "$self"}]}, {"begin": "(^|(?<=[&;|]))\\s*(if)(?=\\s|;|&|$)", "beginCaptures": {"2": {"name": "keyword.control.shell"}}, "comment": "Restrict match to avoid matching in lines like `dd if=/dev/sda1 …`", "end": "(?<=^|;|&|\\s)(fi)(?=\\s|;|&|$)", "endCaptures": {"1": {"name": "keyword.control.shell"}}, "name": "meta.scope.if-block.shell", "patterns": [{"include": "$self"}]}]}, "math": {"patterns": [{"include": "#variable"}, {"match": "\\+{1,2}|-{1,2}|!|~|\\*{1,2}|/|%|<[<=]?|>[>=]?|==|!=|\\^|\\|{1,2}|&{1,2}|\\?|\\:|,|=|[*/%+\\-&^|]=|<<=|>>=", "name": "keyword.operator.arithmetic.shell"}, {"match": "0[xX]\\h+", "name": "constant.numeric.hex.shell"}, {"match": "0\\d+", "name": "constant.numeric.octal.shell"}, {"match": "\\d{1,2}#[0-9a-zA-Z@_]+", "name": "constant.numeric.other.shell"}, {"match": "\\d+", "name": "constant.numeric.integer.shell"}]}, "pathname": {"patterns": [{"match": "(?<=\\s|:|=|^)~", "name": "keyword.operator.tilde.shell"}, {"match": "\\*|\\?", "name": "keyword.operator.glob.shell"}, {"begin": "([?*+@!])(\\()", "beginCaptures": {"1": {"name": "keyword.operator.extglob.shell"}, "2": {"name": "punctuation.definition.extglob.shell"}}, "end": "(\\))", "endCaptures": {"1": {"name": "punctuation.definition.extglob.shell"}}, "name": "meta.structure.extglob.shell", "patterns": [{"include": "$self"}]}]}, "pipeline": {"patterns": [{"match": "(?<=^|;|&|\\s)(time)(?=\\s|;|&|$)", "name": "keyword.other.shell"}, {"match": "[|!]", "name": "keyword.operator.pipe.shell"}]}, "redirection": {"patterns": [{"begin": "[><]\\(", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.shell"}}, "end": "\\)", "endCaptures": {"0": {"name": "punctuation.definition.string.end.shell"}}, "name": "string.interpolated.process-substitution.shell", "patterns": [{"include": "$self"}]}, {"comment": "valid: &>word >&word >word [n]>&[n] [n]<word [n]>word [n]>>word [n]<&word (last one is duplicate)", "match": "&>|\\d*>&\\d*|\\d*(>>|>|<)|\\d*<&|\\d*<>", "name": "keyword.operator.redirect.shell"}]}, "string": {"patterns": [{"match": "\\\\.", "name": "constant.character.escape.shell"}, {"begin": "'", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.shell"}}, "end": "'", "endCaptures": {"0": {"name": "punctuation.definition.string.end.shell"}}, "name": "string.quoted.single.shell"}, {"begin": "\\$?\"", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.shell"}}, "end": "\"", "endCaptures": {"0": {"name": "punctuation.definition.string.end.shell"}}, "name": "string.quoted.double.shell", "patterns": [{"match": "\\\\[\\$`\"\\\\\\n]", "name": "constant.character.escape.shell"}, {"include": "#variable"}, {"include": "#interpolation"}]}, {"begin": "\\$'", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.shell"}}, "end": "'", "endCaptures": {"0": {"name": "punctuation.definition.string.end.shell"}}, "name": "string.quoted.single.dollar.shell", "patterns": [{"match": "\\\\(a|b|e|f|n|r|t|v|\\\\|')", "name": "constant.character.escape.ansi-c.shell"}, {"match": "\\\\[0-9]{3}", "name": "constant.character.escape.octal.shell"}, {"match": "\\\\x[0-9a-fA-F]{2}", "name": "constant.character.escape.hex.shell"}, {"match": "\\\\c.", "name": "constant.character.escape.control-char.shell"}]}]}, "support": {"patterns": [{"match": "(?<=^|;|&|\\s)(?::|\\.)(?=\\s|;|&|$)", "name": "support.function.builtin.shell"}, {"match": "(?<=^|;|&|\\s)(?:alias|bg|bind|break|builtin|caller|cd|command|compgen|complete|dirs|disown|echo|enable|eval|exec|exit|false|fc|fg|getopts|hash|help|history|jobs|kill|let|logout|mapfile|popd|printf|pushd|pwd|read(array)?|readonly|set|shift|shopt|source|suspend|test|times|trap|true|type|ulimit|umask|unalias|unset|wait)(?=\\s|;|&|$)", "name": "support.function.builtin.shell"}]}, "variable": {"patterns": [{"captures": {"1": {"name": "punctuation.definition.variable.shell"}}, "match": "(\\$)[a-zA-Z_][a-zA-Z0-9_]*", "name": "variable.other.normal.shell"}, {"captures": {"1": {"name": "punctuation.definition.variable.shell"}}, "match": "(\\$)[-*@#?$!0_]", "name": "variable.other.special.shell"}, {"captures": {"1": {"name": "punctuation.definition.variable.shell"}}, "match": "(\\$)[1-9]", "name": "variable.other.positional.shell"}, {"begin": "\\$\\{", "captures": {"0": {"name": "punctuation.definition.variable.shell"}}, "end": "\\}", "name": "variable.other.bracket.shell", "patterns": [{"match": "!|:[-=?+]?|\\*|@|#{1,2}|%{1,2}|/", "name": "keyword.operator.expansion.shell"}, {"captures": {"1": {"name": "punctuation.section.array.shell"}, "3": {"name": "punctuation.section.array.shell"}}, "match": "(\\[)([^\\]]+)(\\])"}, {"include": "#string"}, {"include": "#variable"}, {"include": "#interpolation"}]}]}}, "scopeName": "source.shell", "uuid": "DDEEA3ED-6B1C-11D9-8B10-000D93589AF6", "version": "https://github.com/textmate/shellscript.tmbundle/commit/2677fdc83ed9d6a517d5d204e003f49141fc72e4"}