{"scopeName": "source.css.scss", "name": "SCSS", "fileTypes": ["scss", "css.scss", "css.scss.erb", "scss.erb"], "patterns": [{"include": "#variable_setting"}, {"include": "#at_rule_include"}, {"include": "#at_rule_import"}, {"include": "#general"}, {"include": "#flow_control"}, {"include": "#rules"}, {"include": "#property_list"}, {"include": "#at_rule_mixin"}, {"include": "#at_rule_media"}, {"include": "#at_rule_function"}, {"include": "#at_rule_charset"}, {"include": "#at_rule_option"}, {"include": "#at_rule_namespace"}, {"include": "#at_rule_fontface"}, {"include": "#at_rule_page"}, {"include": "#at_rule_keyframes"}, {"include": "#at_rule_at_root"}, {"include": "#at_rule_supports"}], "repository": {"at_rule__": {"comment": "Note how all @rules are prefixed."}, "at_rule_charset": {"begin": "\\s*((@)charset\\b)\\s*", "captures": {"1": {"name": "keyword.control.at-rule.charset.scss"}, "2": {"name": "punctuation.definition.keyword.scss"}}, "comment": "Charset", "end": "\\s*((?=;|$))", "name": "meta.at-rule.charset.scss", "patterns": [{"include": "#variable"}, {"include": "#string_single"}, {"include": "#string_double"}]}, "at_rule_content": {"begin": "\\s*((@)content\\b)\\s*", "captures": {"1": {"name": "keyword.control.content.scss"}}, "end": "\\s*((?=;))", "name": "meta.content.scss", "patterns": [{"include": "#variable"}, {"include": "#selectors"}, {"include": "#property_values"}]}, "at_rule_each": {"begin": "\\s*((@)each\\b)\\s*", "captures": {"1": {"name": "keyword.control.each.scss"}, "2": {"name": "punctuation.definition.keyword.scss"}}, "end": "\\s*((?=}))", "name": "meta.at-rule.each.scss", "patterns": [{"match": "\\b(in|,)\\b", "name": "keyword.control.operator"}, {"include": "#variable"}, {"include": "#property_values"}, {"include": "$self"}]}, "at_rule_else": {"begin": "\\s*((@)else(\\s*(if)?))\\s*", "captures": {"1": {"name": "keyword.control.else.scss"}, "2": {"name": "punctuation.definition.keyword.scss"}}, "end": "\\s*(?={)", "name": "meta.at-rule.else.scss", "patterns": [{"include": "#conditional_operators"}, {"include": "#variable"}, {"include": "#property_values"}]}, "at_rule_extend": {"begin": "\\s*((@)extend\\b)\\s*", "captures": {"1": {"name": "keyword.control.at-rule.import.scss"}, "2": {"name": "punctuation.definition.keyword.scss"}}, "end": "\\s*(?=;)", "name": "meta.at-rule.import.scss", "patterns": [{"include": "#variable"}, {"include": "#selectors"}, {"include": "#property_values"}]}, "at_rule_fontface": {"patterns": [{"begin": "^\\s*((@)font-face\\b)", "beginCaptures": {"1": {"name": "keyword.control.at-rule.fontface.scss"}, "2": {"name": "punctuation.definition.keyword.scss"}}, "end": "\\s*(?={)", "name": "meta.at-rule.fontface.scss", "patterns": [{"include": "#function_attributes"}]}]}, "at_rule_for": {"begin": "\\s*((@)for\\b)\\s*", "captures": {"1": {"name": "keyword.control.for.scss"}, "2": {"name": "punctuation.definition.keyword.scss"}}, "end": "\\s*(?={)", "name": "meta.at-rule.for.scss", "patterns": [{"match": "(==|!=|<=|>=|<|>|from|to|through)", "name": "keyword.control.operator"}, {"include": "#variable"}, {"include": "#property_values"}, {"include": "$self"}]}, "at_rule_function": {"patterns": [{"begin": "\\s*((@)function\\b)\\s*", "captures": {"1": {"name": "keyword.control.at-rule.function.scss"}, "2": {"name": "punctuation.definition.keyword.scss"}, "3": {"name": "entity.name.function.scss"}}, "comment": "Function with Attributes", "end": "\\s*(?={)", "name": "meta.at-rule.function.scss", "patterns": [{"include": "#function_attributes"}]}, {"captures": {"1": {"name": "keyword.control.at-rule.function.scss"}, "2": {"name": "punctuation.definition.keyword.scss"}, "3": {"name": "entity.name.function.scss"}}, "comment": "Simple Function", "match": "\\s*((@)function\\b)\\s*", "name": "meta.at-rule.function.scss"}]}, "at_rule_if": {"begin": "\\s*((@)if\\b)\\s*", "captures": {"1": {"name": "keyword.control.if.scss"}, "2": {"name": "punctuation.definition.keyword.scss"}}, "end": "\\s*(?={)", "name": "meta.at-rule.if.scss", "patterns": [{"include": "#conditional_operators"}, {"include": "#variable"}, {"include": "#property_values"}]}, "at_rule_import": {"begin": "\\s*((@)import\\b)\\s*", "captures": {"1": {"name": "keyword.control.at-rule.import.scss"}, "2": {"name": "punctuation.definition.keyword.scss"}}, "end": "\\s*((?=;)|(?=}))", "name": "meta.at-rule.import.scss", "patterns": [{"include": "#variable"}, {"include": "#string_single"}, {"include": "#string_double"}, {"include": "#functions"}, {"include": "#comment_line"}]}, "at_rule_include": {"patterns": [{"begin": "(?<=@include)\\s+([\\w-]+)\\s*(\\()", "beginCaptures": {"1": {"name": "entity.name.function.scss"}, "2": {"name": "punctuation.definition.parameters.begin.bracket.round.scss"}}, "end": "\\)", "endCaptures": {"0": {"name": "punctuation.definition.parameters.end.bracket.round.scss"}}, "name": "meta.at-rule.include.scss", "patterns": [{"include": "#function_attributes"}]}, {"match": "(?<=@include)\\s+([\\w-]+)", "captures": {"0": {"name": "meta.at-rule.include.scss"}, "1": {"name": "entity.name.function.scss"}}}, {"match": "((@)include)\\b", "captures": {"0": {"name": "meta.at-rule.include.scss"}, "1": {"name": "keyword.control.at-rule.include.scss"}, "2": {"name": "punctuation.definition.keyword.scss"}}}]}, "at_rule_keyframes": {"begin": "(?<=^|\\s)(@)(?:-(?:webkit|moz)-)?keyframes\\b", "beginCaptures": {"0": {"name": "keyword.control.at-rule.keyframes.scss"}, "1": {"name": "punctuation.definition.keyword.scss"}}, "end": "(?<=})", "name": "meta.at-rule.keyframes.scss", "patterns": [{"match": "(?<=@keyframes)\\s+((?:[_A-Za-z][-\\w]|-[_A-Za-z])[-\\w]*)", "captures": {"1": {"name": "entity.name.function.scss"}}}, {"begin": "(?<=@keyframes)\\s+(\")", "beginCaptures": {"1": {"name": "punctuation.definition.string.begin.scss"}}, "end": "\"", "endCaptures": {"0": {"name": "punctuation.definition.string.end.scss"}}, "name": "string.quoted.double.scss", "contentName": "entity.name.function.scss", "patterns": [{"match": "\\\\(\\h{1,6}|.)", "name": "constant.character.escape.scss"}, {"include": "#interpolation"}]}, {"begin": "(?<=@keyframes)\\s+(')", "beginCaptures": {"1": {"name": "punctuation.definition.string.begin.scss"}}, "end": "'", "endCaptures": {"0": {"name": "punctuation.definition.string.end.scss"}}, "name": "string.quoted.single.scss", "contentName": "entity.name.function.scss", "patterns": [{"match": "\\\\(\\h{1,6}|.)", "name": "constant.character.escape.scss"}, {"include": "#interpolation"}]}, {"begin": "{", "beginCaptures": {"0": {"name": "punctuation.section.keyframes.begin.scss"}}, "end": "}", "endCaptures": {"0": {"name": "punctuation.section.keyframes.end.scss"}}, "patterns": [{"match": "\\b(?:(?:100|[1-9]\\d|\\d)%|from|to)(?=\\s*{)", "name": "entity.other.attribute-name.scss"}, {"include": "#flow_control"}, {"include": "#interpolation"}, {"include": "#property_list"}, {"include": "#rules"}]}]}, "at_rule_media": {"patterns": [{"begin": "^\\s*((@)media\\b)\\s*", "end": "\\s*(?={)", "captures": {"1": {"name": "keyword.control.at-rule.media.scss"}, "2": {"name": "punctuation.definition.keyword.scss"}}, "name": "meta.at-rule.media.scss", "patterns": [{"match": "\\b(only)\\b", "name": "keyword.control.operator"}, {"include": "#media_features"}, {"include": "#property_values"}, {"include": "#variable"}, {"include": "#conditional_operators"}, {"include": "#media_types"}, {"include": "#property_names"}]}]}, "at_rule_mixin": {"patterns": [{"begin": "(?<=@mixin)\\s+([\\w-]+)\\s*(\\()", "beginCaptures": {"1": {"name": "entity.name.function.scss"}, "2": {"name": "punctuation.definition.parameters.begin.bracket.round.scss"}}, "end": "\\)", "endCaptures": {"0": {"name": "punctuation.definition.parameters.end.bracket.round.scss"}}, "name": "meta.at-rule.mixin.scss", "patterns": [{"include": "#function_attributes"}]}, {"match": "(?<=@mixin)\\s+([\\w-]+)", "captures": {"1": {"name": "entity.name.function.scss"}}, "name": "meta.at-rule.mixin.scss"}, {"match": "((@)mixin)\\b", "captures": {"1": {"name": "keyword.control.at-rule.mixin.scss"}, "2": {"name": "punctuation.definition.keyword.scss"}}, "name": "meta.at-rule.mixin.scss"}]}, "at_rule_namespace": {"patterns": [{"begin": "(?<=@namespace)\\s+(?=url)", "end": "(?=;|$)", "name": "meta.at-rule.namespace.scss", "patterns": [{"include": "#property_values"}, {"include": "#string_single"}, {"include": "#string_double"}]}, {"begin": "(?<=@namespace)\\s+([\\w-]*)", "captures": {"1": {"name": "entity.name.namespace-prefix.scss"}}, "end": "(?=;|$)", "name": "meta.at-rule.namespace.scss", "patterns": [{"include": "#variables"}, {"include": "#property_values"}, {"include": "#string_single"}, {"include": "#string_double"}]}, {"match": "((@)namespace)\\b", "captures": {"1": {"name": "keyword.control.at-rule.namespace.scss"}, "2": {"name": "punctuation.definition.keyword.scss"}}, "name": "meta.at-rule.namespace.scss"}]}, "at_rule_option": {"captures": {"1": {"name": "keyword.control.at-rule.charset.scss"}, "2": {"name": "punctuation.definition.keyword.scss"}}, "comment": "Option", "match": "^\\s*((@)option\\b)\\s*", "name": "meta.at-rule.option.scss"}, "at_rule_page": {"patterns": [{"begin": "^\\s*((@)page)(?=:|\\s)\\s*([-:\\w]*)", "captures": {"1": {"name": "keyword.control.at-rule.page.scss"}, "2": {"name": "punctuation.definition.keyword.scss"}, "3": {"name": "entity.name.function.scss"}}, "comment": "Page with Attributes", "end": "\\s*(?={)", "name": "meta.at-rule.page.scss"}]}, "at_rule_return": {"begin": "\\s*((@)(return)\\b)", "captures": {"1": {"name": "keyword.control.return.scss"}, "2": {"name": "punctuation.definition.keyword.scss"}}, "end": "\\s*((?=;))", "name": "meta.at-rule.return.scss", "patterns": [{"include": "#variable"}, {"include": "#property_values"}]}, "at_rule_at_root": {"begin": "\\s*((@)(at-root))(\\s+|$)", "end": "\\s*(?={)", "beginCaptures": {"1": {"name": "keyword.control.at-rule.at-root.scss"}, "2": {"name": "punctuation.definition.keyword.scss"}}, "name": "meta.at-rule.at-root.scss", "patterns": [{"include": "#function_attributes"}, {"include": "#functions"}, {"include": "#selectors"}]}, "at_rule_supports": {"begin": "(?<=^|\\s)(@)supports\\b", "captures": {"0": {"name": "keyword.control.at-rule.supports.scss"}, "1": {"name": "punctuation.definition.keyword.scss"}}, "end": "(?={)|$", "name": "meta.at-rule.supports.scss", "patterns": [{"include": "#logical_operators"}, {"include": "#properties"}, {"match": "\\(", "name": "punctuation.definition.condition.begin.bracket.round.scss"}, {"match": "\\)", "name": "punctuation.definition.condition.end.bracket.round.scss"}]}, "at_rule_warn": {"begin": "\\s*((@)(warn|debug|error)\\b)\\s*", "captures": {"1": {"name": "keyword.control.warn.scss"}, "2": {"name": "punctuation.definition.keyword.scss"}}, "end": "\\s*(?=;)", "name": "meta.at-rule.warn.scss", "patterns": [{"include": "#variable"}, {"include": "#string_double"}, {"include": "#string_single"}]}, "at_rule_while": {"begin": "\\s*((@)while\\b)\\s*", "captures": {"1": {"name": "keyword.control.while.scss"}, "2": {"name": "punctuation.definition.keyword.scss"}}, "end": "\\s*(?=})", "name": "meta.at-rule.while.scss", "patterns": [{"include": "#conditional_operators"}, {"include": "#variable"}, {"include": "#property_values"}, {"include": "$self"}]}, "comment_block": {"begin": "/\\*", "beginCaptures": {"0": {"name": "punctuation.definition.comment.scss"}}, "end": "\\*/", "endCaptures": {"0": {"name": "punctuation.definition.comment.scss"}}, "name": "comment.block.scss"}, "comment_line": {"begin": "//", "beginCaptures": {"0": {"name": "punctuation.definition.comment.scss"}}, "end": "\\n", "name": "comment.line.scss"}, "constant_color": {"comment": "http://www.w3.org/TR/css3-color/#svg-color", "match": "\\b(aliceblue|antiquewhite|aqua|aquamarine|azure|beige|bisque|black|blanchedalmond|blue|blueviolet|brown|burlywood|cadetblue|chartreuse|chocolate|coral|cornflowerblue|cornsilk|crimson|cyan|darkblue|darkcyan|darkgoldenrod|darkgray|darkgreen|darkgrey|darkkhaki|darkmagenta|darkolivegreen|darkorange|darkorchid|darkred|darksalmon|darkseagreen|darkslateblue|darkslategray|darkslategrey|darkturquoise|darkviolet|deeppink|deepskyblue|dimgray|dimgrey|dodgerblue|firebrick|floralwhite|forestgreen|fuchsia|gainsboro|ghostwhite|gold|goldenrod|gray|green|greenyellow|grey|honeydew|hotpink|indianred|indigo|ivory|khaki|lavender|lavenderblush|lawngreen|lemonchiffon|lightblue|lightcoral|lightcyan|lightgoldenrodyellow|lightgray|lightgreen|lightgrey|lightpink|lightsalmon|lightseagreen|lightskyblue|lightslategray|lightslategrey|lightsteelblue|lightyellow|lime|limegreen|linen|magenta|maroon|mediumaquamarine|mediumblue|mediumorchid|mediumpurple|mediumseagreen|mediumslateblue|mediumspringgreen|mediumturquoise|mediumvioletred|midnightblue|mintcream|mistyrose|moccasin|navajowhite|navy|oldlace|olive|olivedrab|orange|orangered|orchid|palegoldenrod|palegreen|paleturquoise|palevioletred|papayawhip|peachpuff|peru|pink|plum|powderblue|purple|red|rosybrown|royalblue|saddlebrown|salmon|sandybrown|seagreen|seashell|sienna|silver|skyblue|slateblue|slategray|slategrey|snow|springgreen|steelblue|tan|teal|thistle|tomato|turquoise|violet|wheat|white|whitesmoke|yellow|yellowgreen)\\b", "name": "support.constant.color.w3c-standard-color-name.scss"}, "constant_default": {"match": "!default", "name": "keyword.other.default.scss"}, "constant_deprecated_color": {"comment": "These colours are deprecated from CSS color module level 3 http://www.w3.org/TR/css3-color/#css2-system", "match": "\\b(ActiveBorder|ActiveCaption|AppWorkspace|Background|ButtonFace|ButtonHighlight|ButtonShadow|ButtonText|CaptionText|GrayText|Highlight|HighlightText|InactiveBorder|InactiveCaption|InactiveCaptionText|InfoBackground|InfoText|Menu|MenuText|Scrollbar|ThreeDDarkShadow|ThreeDFace|ThreeDHighlight|ThreeDLightShadow|ThreeDShadow|Window|WindowFrame|WindowText)\\b", "name": "invalid.deprecated.color.system.css.scss"}, "constant_font": {"match": "(\\b(?i:arial|century|comic|courier|garamond|georgia|helvetica|impact|lucida|symbol|system|tahoma|times|trebuchet|utopia|verdana|webdings|sans-serif|serif|monospace)\\b)", "name": "support.constant.font-name.scss"}, "constant_functions": {"begin": "([\\w-]+)(\\()", "beginCaptures": {"1": {"name": "support.function.misc.scss"}, "2": {"name": "punctuation.section.function.scss"}}, "end": "(\\))", "endCaptures": {"1": {"name": "punctuation.section.function.scss"}}, "patterns": [{"include": "#parameters"}]}, "constant_hex": {"captures": {"1": {"name": "punctuation.definition.constant.scss"}}, "match": "(#)([0-9a-fA-F]{3}|[0-9a-fA-F]{6})\\b", "name": "constant.numeric.color.hex-value.scss"}, "constant_important": {"match": "!important", "name": "keyword.other.important.scss"}, "constant_mathematical_symbols": {"match": "\\b(\\+|-|\\*|/)\\b", "name": "support.constant.mathematical-symbols.scss"}, "constant_number": {"match": "(\\b([0-9]+(\\.[0-9]+)?)|\\B\\.[0-9]+)(?=\\s*(ch|cm|deg|dpi|dpcm|dppx|em|ex|grad|in|mm|mozmm|ms|pc|pt|px|rad|rem|turn|s|vh|vmin|vmax|vw|\\b))", "name": "constant.numeric.scss"}, "constant_optional": {"match": "!optional", "name": "keyword.other.optional.scss"}, "constant_property_value": {"match": "\\b(absolute|all-scroll|always|armenian|auto|baseline|below|bidi-override|block|bold|bolder|both|bottom|border-box|break-all|break-word|butt|capitalize|center|char|circle|cjk-ideographic|col-resize|collapse|column-reverse|column|contain|content-box|cover|crosshair|currentColor|dashed|decimal-leading-zero|decimal|default|disabled|disc|distribute-all-lines|distribute-letter|distribute-space|distribute|dotted|double|e-resize|ease-in-out|ease-in|ease-out|ease|ellipsis|fill|fixed|flex-end|flex-start|flex|georgian|grid|groove|hand|hebrew|help|hidden|hiragana-iroha|hiragana|horizontal|ideograph-alpha|ideograph-numeric|ideograph-parenthesis|ideograph-space|inactive|inherit|inline-block|inline-flex|inline-grid|inline-table|inline|inset|inside|inter-ideograph|inter-word|italic|justify|katakana-iroha|katakana|keep-all|landscape|left|lighter|line-edge|line-through|line|linear|list-item|loose|lower-alpha|lower-greek|lower-latin|lower-roman|lowercase|lr-tb|ltr|manipulation|medium|middle|move|n-resize|ne-resize|newspaper|no-drop|no-repeat|nw-resize|none|normal|not-allowed|nowrap|null|oblique|outset|outside|overline|pan-(x|y|left|right|up|down)|pointer|portrait|preserve-3d|progress|relative|repeat-x|repeat-y|repeat|right|ridge|round|row-resize|row-reverse|row|rtl|ruby-base-container|ruby-base|ruby-text-container|ruby-text|ruby|s-resize|scale-down|scroll|se-resize|separate|small-caps|solid|space-around|space-between|square|static|step-end|step-start|stretch|strict|super|sw-resize|table-caption|table-cell|table-column-group|table-column|table-footer-group|table-header-group|table-row-group|table-row|table|tb-rl|text-bottom|text-top|text|thick|thin|top|transparent|underline|upper-alpha|upper-latin|upper-roman|uppercase|vertical-ideographic|vertical-text|visible|w-resize|wait|whitespace|wrap|wrap-reverse|zero|true|false|vertical|horizontal)\\b", "name": "support.constant.property-value.scss"}, "constant_rgb": {"match": "(?x)\n(?<!scale3d\\(|translate3d\\(|rotate3d\\() # scale3d, translate3d, and rotate3d don't use RGB values\n\\b\n(0*(?:1?[0-9]{1,2})|(?:2(?:[0-4][0-9]|5[0-5])))\n\\s*(,)\\s*\n(0*(?:1?[0-9]{1,2})|(?:2(?:[0-4][0-9]|5[0-5])))\n\\s*(,)\\s*\n(0*(?:1?[0-9]{1,2})|(?:2(?:[0-4][0-9]|5[0-5])))\n\\b", "captures": {"1": {"name": "constant.numeric.color.rgb-value.scss"}, "2": {"name": "punctuation.separator.delimiter.scss"}, "3": {"name": "constant.numeric.color.rgb-value.scss"}, "4": {"name": "punctuation.separator.delimiter.scss"}, "5": {"name": "constant.numeric.color.rgb-value.scss"}}}, "constant_rgb_percentage": {"match": "(?x)\n(?<!scale3d\\(|translate3d\\(|rotate3d\\() # scale3d, translate3d, and rotate3d don't use RGB percentages\n\\b\n([0-9]{1,2}|100)\\s*(%)\n\\s*(,)\\s*\n([0-9]{1,2}|100)\\s*(%)\n\\s*(,)\\s*\n([0-9]{1,2}|100)\\s*(%)", "captures": {"1": {"name": "constant.numeric.color.rgb-percentage.scss"}, "2": {"name": "keyword.other.unit.scss"}, "3": {"name": "punctuation.separator.delimiter.scss"}, "4": {"name": "constant.numeric.color.rgb-percentage.scss"}, "5": {"name": "keyword.other.unit.scss"}, "6": {"name": "punctuation.separator.delimiter.scss"}, "7": {"name": "constant.numeric.color.rgb-percentage.scss"}, "8": {"name": "keyword.other.unit.scss"}}}, "constant_sass_functions": {"begin": "(headings|stylesheet-url|rgba?|hsla?|ie-hex-str|red|green|blue|alpha|opacity|hue|saturation|lightness|prefixed|prefix|-moz|-svg|-css2|-pie|-webkit|-ms|font-(?:files|url)|grid-image|image-(?:width|height|url|color)|sprites?|sprite-(?:map|map-name|file|url|position)|inline-(?:font-files|image)|opposite-position|grad-point|grad-end-position|color-stops|color-stops-in-percentages|grad-color-stops|(?:radial|linear)-(?:gradient|svg-gradient)|opacify|fade-?in|transparentize|fade-?out|lighten|darken|saturate|desaturate|grayscale|adjust-(?:hue|lightness|saturation|color)|scale-(?:lightness|saturation|color)|change-color|spin|complement|invert|mix|-compass-(?:list|space-list|slice|nth|list-size)|blank|compact|nth|first-value-of|join|length|append|nest|append-selector|headers|enumerate|range|percentage|unitless|unit|if|type-of|comparable|elements-of-type|quote|unquote|escape|e|sin|cos|tan|abs|round|ceil|floor|pi|translate(?:X|Y))(\\()", "beginCaptures": {"1": {"name": "support.function.misc.scss"}, "2": {"name": "punctuation.section.function.scss"}}, "end": "(\\))", "endCaptures": {"1": {"name": "punctuation.section.function.scss"}}, "patterns": [{"include": "#parameters"}]}, "constant_unit": {"match": "(?<=[\\d])(ch|cm|deg|dpi|dpcm|dppx|em|ex|grad|in|mm|mozmm|ms|pc|pt|px|rad|rem|turn|s|vh|vmin|vmax|vw)\\b|%", "name": "keyword.other.unit.scss"}, "flow_control": {"patterns": [{"include": "#at_rule_if"}, {"include": "#at_rule_else"}, {"include": "#at_rule_warn"}, {"include": "#at_rule_for"}, {"include": "#at_rule_while"}, {"include": "#at_rule_each"}, {"include": "#at_rule_return"}]}, "function_attributes": {"patterns": [{"match": ":", "name": "punctuation.separator.key-value.scss"}, {"include": "#general"}, {"include": "#property_values"}, {"comment": "We even have error highlighting <3", "match": "[={}\\?;@]", "name": "invalid.illegal.scss"}]}, "functions": {"patterns": [{"begin": "([\\w-]{1,})(\\()\\s*", "beginCaptures": {"1": {"name": "support.function.misc.scss"}, "2": {"name": "punctuation.section.function.scss"}}, "end": "(\\))", "endCaptures": {"1": {"name": "punctuation.section.function.scss"}}, "patterns": [{"include": "#parameters"}]}, {"match": "([\\w-]{1,})", "name": "support.function.misc.scss"}]}, "general": {"comment": "Stuff that should be everywhere", "patterns": [{"include": "#variable"}, {"include": "#comment_block"}, {"include": "#comment_line"}]}, "interpolation": {"begin": "#{", "beginCaptures": {"0": {"name": "punctuation.definition.interpolation.begin.bracket.curly.scss"}}, "end": "}", "endCaptures": {"0": {"name": "punctuation.definition.interpolation.end.bracket.curly.scss"}}, "name": "variable.interpolation.scss", "patterns": [{"include": "#property_values"}, {"include": "#variable"}]}, "conditional_operators": {"patterns": [{"include": "#comparison_operators"}, {"include": "#logical_operators"}]}, "comparison_operators": {"match": "==|!=|<=|>=|<|>", "name": "keyword.operator.comparison.scss"}, "logical_operators": {"match": "\\b(not\\b|or\\b|and\\b)", "name": "keyword.operator.logical.scss"}, "map": {"begin": "\\(", "beginCaptures": {"0": {"name": "punctuation.definition.map.begin.bracket.round.scss"}}, "end": "\\)", "endCaptures": {"0": {"name": "punctuation.definition.map.end.bracket.round.scss"}}, "name": "meta.set.variable.map.scss", "patterns": [{"include": "#comment_block"}, {"include": "#comment_line"}, {"match": "\\b([\\w-]+)\\s*(:)", "captures": {"1": {"name": "support.type.map.key.scss"}, "2": {"name": "punctuation.separator.key-value.scss"}}}, {"match": ",", "name": "punctuation.separator.delimiter.scss"}, {"include": "#property_values"}, {"include": "#variable"}, {"include": "#map"}]}, "media_attributes": {"patterns": [{"match": ":", "name": "punctuation.separator.key-value.scss"}, {"include": "#general"}, {"include": "#property_name"}, {"include": "#property_values"}, {"comment": "We even have error highlighting <3", "match": "[={}\\?@]", "name": "invalid.illegal.scss"}]}, "media_features": {"patterns": [{"match": "\\b(min-device-aspect-ratio|max-device-aspect-ratio|device-aspect-ratio|min-aspect-ratio|max-aspect-ratio|aspect-ratio|min-device-height|max-device-height|device-height|min-device-width|max-device-width|device-width|min-monochrome|max-monochrome|monochrome|min-color-index|max-color-index|color-index|min-color|max-color|color|orientation|scan|min-resolution|max-resolution|resolution|grid|min-width|max-width|width)\\b", "name": "support.type.property-name.media.css"}]}, "media_types": {"patterns": [{"match": "\\b(all|aural|braille|embossed|handheld|print|projection|screen|tty|tv)\\b", "name": "support.constant.media.css"}]}, "operators": {"match": "[-+*/](?!\\s*[-+*/])", "name": "keyword.operator.css"}, "parameters": {"patterns": [{"include": "#variable"}, {"include": "#property_values"}, {"include": "#comment_block"}, {"begin": "\\(", "beginCaptures": {"0": {"name": "punctuation.definition.begin.bracket.round.scss"}}, "end": "\\)", "endCaptures": {"0": {"name": "punctuation.definition.end.bracket.round.scss"}}, "patterns": [{"include": "#function_attributes"}]}, {"match": "[^'\",) \\t]+", "name": "variable.parameter.url.scss"}, {"match": ",", "name": "punctuation.separator.delimiter.scss"}]}, "properties": {"patterns": [{"begin": "(?<![-a-z])(?=[-a-z])", "end": "$|(?![-a-z])", "name": "meta.property-name.scss", "patterns": [{"include": "#property_names"}, {"include": "#at_rule_include"}]}, {"begin": "(:)\\s*(?!(\\s*{))", "beginCaptures": {"1": {"name": "punctuation.separator.key-value.scss"}}, "comment": "Kuroir: fixed nested elements for sass.", "end": "\\s*(;|(?=}|\\)))", "endCaptures": {"1": {"name": "punctuation.terminator.rule.scss"}}, "contentName": "meta.property-value.scss", "patterns": [{"include": "#general"}, {"include": "#property_values"}]}]}, "property_list": {"begin": "{", "beginCaptures": {"0": {"name": "punctuation.section.property-list.begin.bracket.curly.scss"}}, "end": "}", "endCaptures": {"0": {"name": "punctuation.section.property-list.end.bracket.curly.scss"}}, "name": "meta.property-list.scss", "patterns": [{"include": "#flow_control"}, {"include": "#rules"}, {"include": "#properties"}, {"include": "$self"}]}, "property_name": {"comment": "Reversal order is important; KEEP IT. Also when adding new properties add them to the error checker below!", "match": "((?<=\\s|^)(-webkit-[A-Za-z-]+|-moz-[A-Za-z-]+|-ms-[A-Za-z-]+))|\\b([0-9]{1,3}%|zoom|z-index|y|x|wrap|word-wrap|word-spacing|word-break|word|width|widows|will-change|white-space-collapse|white-space|white|weight|volume|voice-volume|voice-stress|voice-rate|voice-pitch-range|voice-pitch|voice-family|voice-duration|voice-balance|voice|visibility|vertical-align|variant|user-select|up|unicode-bidi|unicode|trim|transition-timing-function|transition-property|transition-duration|transition-delay|transition|transform|touch-action|top-width|top-style|top-right-radius|top-left-radius|top-color|top|timing-function|text-wrap|text-transform|text-shadow|text-replace|text-outline|text-justify|text-indent|text-height|text-emphasis|text-decoration|text-align-last|text-align|text|target-position|target-new|target-name|target|table-layout|tab-size|style-type|style-position|style-image|style|stroke|string-set|stretch|stress|stacking-strategy|stacking-shift|stacking-ruby|stacking|src|speed|speech-rate|speech|speak-punctuation|speak-numeral|speak-header|speak|span|spacing|space-collapse|space|sizing|size-adjust|size|shadow|respond-to|rule-width|rule-style|rule-color|rule|ruby-span|ruby-position|ruby-overhang|ruby-align|ruby|rows|rotation-point|rotation|role|right-width|right-style|right-color|right|richness|rest-before|rest-after|rest|resource|resolution|resize|reset|replace|repeat|rendering-intent|rate|radius|quotes|punctuation-trim|punctuation|property|profile|presentation-level|presentation|position|pointer-events|point|play-state|play-during|play-count|pitch-range|pitch|phonemes|perspective|pause-before|pause-after|pause|page-policy|page-break-inside|page-break-before|page-break-after|page|padding-top|padding-right|padding-left|padding-bottom|padding|pack|overhang|overflow-y|overflow-x|overflow-wrap|overflow-style|overflow|outline-width|outline-style|outline-offset|outline-color|outline|orphans|origin|orientation|orient|ordinal-group|order|opacity|offset|object-position|object-fit|numeral|new|nav-up|nav-right|nav-left|nav-index|nav-down|nav|name|move-to|model|min-width|min-height|min|max-width|max-height|max|marquee-style|marquee-speed|marquee-play-count|marquee-direction|marquee|marks|mark-before|mark-after|marker|mark|margin-top|margin-right|margin-left|margin-bottom|margin|mask-image|list-style-type|list-style-position|list-style-image|list-style|list|lines|line-stacking-strategy|line-stacking-shift|line-stacking-ruby|line-stacking|line-height|line|level|letter-spacing|length|left-width|left-style|left-color|left|label|justify-content|justify|iteration-count|inline-box-align|initial-value|initial-size|initial-before-align|initial-before-adjust|initial-after-align|initial-after-adjust|index|indent|increment|image-resolution|image-orientation|image|icon|hyphens|hyphenate-resource|hyphenate-lines|hyphenate-character|hyphenate-before|hyphenate-after|hyphenate|height|header|hanging-punctuation|grid-rows|grid-columns|grid|gap|font-weight|font-variant|font-variant-ligatures|font-style|font-stretch|font-size-adjust|font-size|font-family|font|float-offset|float|flex-wrap|flex-shrink|flex-grow|flex-group|flex-flow|flex-direction|flex-basis|flex|fit-position|fit|fill|filter|family|empty-cells|emphasis|elevation|duration|drop-initial-value|drop-initial-size|drop-initial-before-align|drop-initial-before-adjust|drop-initial-after-align|drop-initial-after-adjust|drop|down|dominant-baseline|display-role|display-model|display|direction|delay|decoration-break|decoration|cursor|cue-before|cue-after|cue|crop|counter-reset|counter-increment|counter|count|content|columns|column-width|column-span|column-rule-width|column-rule-style|column-rule-color|column-rule|column-gap|column-fill|column-count|column-break-before|column-break-after|column|color-profile|color|collapse|clip|clear|character|caption-side|break-inside|break-before|break-after|break|box-sizing|box-shadow|box-pack|box-orient|box-ordinal-group|box-lines|box-flex-group|box-flex|box-direction|box-decoration-break|box-align|box|bottom-width|bottom-style|bottom-right-radius|bottom-left-radius|bottom-color|bottom|border-width|border-top-width|border-top-style|border-top-right-radius|border-top-left-radius|border-top-color|border-top|border-style|border-spacing|border-right-width|border-right-style|border-right-color|border-right|border-radius|border-length|border-left-width|border-left-style|border-left-color|border-left|border-image|border-color|border-collapse|border-bottom-width|border-bottom-style|border-bottom-right-radius|border-bottom-left-radius|border-bottom-color|border-bottom|border|bookmark-target|bookmark-level|bookmark-label|bookmark|binding|bidi|before|baseline-shift|baseline|balance|background-size|background-repeat|background-position-y|background-position-x|background-position|background-origin|background-image|background-color|background-clip|background-break|background-attachment|background|backface-visibility|azimuth|attachment|appearance|animation-timing-function|animation-play-state|animation-name|animation-iteration-count|animation-fill-mode|animation-duration|animation-direction|animation-delay|animation|alignment-baseline|alignment-adjust|alignment|align-self|align-last|align-items|align-content|align|after|adjust)\\b", "name": "support.type.property-name.scss"}, "property_name_error": {"match": "(?<![a-z-])(?!-(top|right|left|bottom|color|radius|last|inside|width|before|after|value|size|type|position|style|image|strategy|shift|align|adjust)\\b|-webkit-[A-Za-z]+\\b|-moz-[A-Za-z]+\\b|-ms-[A-Za-z]+\\b|z-index\\b|[0-9]{1,3}%\\b|zoom\\b|y\\b|x\\b|wrap\\b|word-wrap\\b|word-spacing\\b|word-break\\b|word\\b|will-change\\b|width\\b|widows\\b|white-space-collapse\\b|white-space\\b|white\\b|weight\\b|volume\\b|voice-volume\\b|voice-stress\\b|voice-rate\\b|voice-pitch-range\\b|voice-pitch\\b|voice-family\\b|voice-duration\\b|voice-balance\\b|voice\\b|visibility\\b|vertical-align\\b|variant\\b|user-select\\b|up\\b|unicode-bidi\\b|unicode\\b|u\\b|tt\\b|trim\\b|transition-timing-function\\b|transition-property\\b|transition-duration\\b|transition-delay\\b|transition\\b|transform\\b|touch-action\\b|top-width\\b|top-style\\b|top-right-radius\\b|top-left-radius\\b|top-color\\b|top\\b|timing-function\\b|text-wrap\\b|text-transform\\b|text-shadow\\b|text-replace\\b|text-outline\\b|text-justify\\b|text-indent\\b|text-height\\b|text-emphasis\\b|text-decoration\\b|text-align-last\\b|text-align\\b|text\\b|target-position\\b|target-new\\b|target-name\\b|target\\b|table-layout\\b|tab-size\\b|style-type\\b|style-position\\b|style-image\\b|style\\b|string-set\\b|stroke\\b|strike\\b|stretch\\b|stress\\b|stacking-strategy\\b|stacking-shift\\b|stacking-ruby\\b|stacking\\b|src\\b|speed\\b|speech-rate\\b|speech\\b|speak-punctuation\\b|speak-numeral\\b|speak-header\\b|speak\\b|span\\b|spacing\\b|space-collapse\\b|space\\b|sizing\\b|size-adjust\\b|size\\b|shadow\\b|s\\b|respond-to\\b|rule-width\\b|rule-style\\b|rule-color\\b|rule\\b|ruby-span\\b|ruby-position\\b|ruby-overhang\\b|ruby-align\\b|ruby\\b|rows\\b|rotation-point\\b|rotation\\b|role\\b|right-width\\b|right-style\\b|right-color\\b|right\\b|richness\\b|rest-before\\b|rest-after\\b|rest\\b|resource\\b|resolution\\b|resize\\b|reset\\b|replace\\b|repeat\\b|rendering-intent\\b|rate\\b|radius\\b|quotes\\b|punctuation-trim\\b|punctuation\\b|property\\b|profile\\b|presentation-level\\b|presentation\\b|position\\b|pointer-events\\b|point\\b|play-state\\b|play-during\\b|play-count\\b|pitch-range\\b|pitch\\b|phonemes\\b|pause-before\\b|pause-after\\b|pause\\b|page-policy\\b|page-break-inside\\b|page-break-before\\b|page-break-after\\b|page\\b|padding-top\\b|padding-right\\b|padding-left\\b|padding-bottom\\b|padding\\b|pack\\b|overhang\\b|overflow-y\\b|overflow-x\\b|overflow-wrap\\b|overflow-style\\b|overflow\\b|outline-width\\b|outline-style\\b|outline-offset\\b|outline-color\\b|outline\\b|orphans\\b|origin\\b|orientation\\b|orient\\b|ordinal-group\\b|order\\b|opacity\\b|offset\\b|object-position\\b|object-fit\\b|numeral\\b|new\\b|nav-up\\b|nav-right\\b|nav-left\\b|nav-index\\b|nav-down\\b|nav\\b|name\\b|move-to\\b|model\\b|min-width\\b|min-height\\b|min\\b|max-width\\b|max-height\\b|max\\b|marquee-style\\b|marquee-speed\\b|marquee-play-count\\b|marquee-direction\\b|marquee\\b|marks\\b|mark-before\\b|mark-after\\b|marker\\b|mark\\b|margin-top\\b|margin-right\\b|margin-left\\b|margin-bottom\\b|margin\\b|mask-image\\b|list-style-type\\b|list-style-position\\b|list-style-image\\b|list-style\\b|list\\b|lines\\b|line-stacking-strategy\\b|line-stacking-shift\\b|line-stacking-ruby\\b|line-stacking\\b|line-height\\b|line\\b|level\\b|letter-spacing\\b|length\\b|left-width\\b|left-style\\b|left-color\\b|left\\b|label\\b|justify-content\\b|justify\\b|iteration-count\\b|inline-box-align\\b|initial-value\\b|initial-size\\b|initial-before-align\\b|initial-before-adjust\\b|initial-after-align\\b|initial-after-adjust\\b|index\\b|indent\\b|increment\\b|image-resolution\\b|image-orientation\\b|image\\b|icon\\b|hyphens\\b|hyphenate-resource\\b|hyphenate-lines\\b|hyphenate-character\\b|hyphenate-before\\b|hyphenate-after\\b|hyphenate\\b|height\\b|header\\b|hanging-punctuation\\b|grid-rows\\b|grid-columns\\b|grid\\b|gap\\b|font-weight\\b|font-variant\\b|font-variant-ligatures\\b|font-style\\b|font-stretch\\b|font-size-adjust\\b|font-size\\b|font-family\\b|font\\b|float-offset\\b|float\\b|flex-wrap\\b|flex-shrink\\b|flex-grow\\b|flex-group\\b|flex-flow\\b|flex-direction\\b|flex-basis\\b|flex\\b|fit-position\\b|fit\\b|fill\\b|filter\\b|family\\b|empty-cells\\b|emphasis\\b|elevation\\b|duration\\b|drop-initial-value\\b|drop-initial-size\\b|drop-initial-before-align\\b|drop-initial-before-adjust\\b|drop-initial-after-align\\b|drop-initial-after-adjust\\b|drop\\b|down\\b|dominant-baseline\\b|display-role\\b|display-model\\b|display\\b|direction\\b|delay\\b|decoration-break\\b|decoration\\b|cursor\\b|cue-before\\b|cue-after\\b|cue\\b|crop\\b|counter-reset\\b|counter-increment\\b|counter\\b|count\\b|content\\b|columns\\b|column-width\\b|column-span\\b|column-rule-width\\b|column-rule-style\\b|column-rule-color\\b|column-rule\\b|column-gap\\b|column-fill\\b|column-count\\b|column-break-before\\b|column-break-after\\b|column\\b|color-profile\\b|color\\b|collapse\\b|clip\\b|clear\\b|character\\b|center\\b|caption-side\\b|break-inside\\b|break-before\\b|break-after\\b|break\\b|box-sizing\\b|box-shadow\\b|box-pack\\b|box-orient\\b|box-ordinal-group\\b|box-lines\\b|box-flex-group\\b|box-flex\\b|box-direction\\b|box-decoration-break\\b|box-align\\b|box\\b|bottom-width\\b|bottom-style\\b|bottom-right-radius\\b|bottom-left-radius\\b|bottom-color\\b|bottom\\b|border-width\\b|border-top-width\\b|border-top-style\\b|border-top-right-radius\\b|border-top-left-radius\\b|border-top-color\\b|border-top\\b|border-style\\b|border-spacing\\b|border-right-width\\b|border-right-style\\b|border-right-color\\b|border-right\\b|border-radius\\b|border-length\\b|border-left-width\\b|border-left-style\\b|border-left-color\\b|border-left\\b|border-image\\b|border-color\\b|border-collapse\\b|border-bottom-width\\b|border-bottom-style\\b|border-bottom-right-radius\\b|border-bottom-left-radius\\b|border-bottom-color\\b|border-bottom\\b|border\\b|bookmark-target\\b|bookmark-level\\b|bookmark-label\\b|bookmark\\b|binding\\b|bidi\\b|big\\b|before\\b|baseline-shift\\b|baseline\\b|balance\\b|background-size\\b|background-repeat\\b|background-position-y\\b|background-position-x\\b|background-position\\b|background-origin\\b|background-image\\b|background-color\\b|background-clip\\b|background-break\\b|background-attachment\\b|background\\b|backface-visibility\\b|azimuth\\b|attachment\\b|applet\\b|appearance\\b|animation-timing-function\\b|animation-play-state\\b|animation-name\\b|animation-iteration-count\\b|animation-fill-mode\\b|animation-duration\\b|animation-direction\\b|animation-delay\\b|animation\\b|alignment-baseline\\b|alignment-adjust\\b|alignment\\b|align-last\\b|align-self\\b|align-items\\b|align-content\\b|align\\b|after\\b|adjust\\b|acronym\\b)[-_a-z]+", "name": "invalid.illegal.scss"}, "property_names": {"patterns": [{"include": "#property_name"}, {"include": "#property_name_error"}]}, "property_values": {"comment": "Stuff that should only be available on values.", "patterns": [{"include": "#string_single"}, {"include": "#string_double"}, {"include": "#constant_functions"}, {"include": "#constant_sass_functions"}, {"include": "#constant_hex"}, {"include": "#constant_rgb"}, {"include": "#constant_rgb_percentage"}, {"include": "#constant_important"}, {"include": "#constant_default"}, {"include": "#constant_optional"}, {"include": "#constant_unit"}, {"include": "#constant_property_value"}, {"include": "#constant_number"}, {"include": "#constant_font"}, {"include": "#constant_color"}, {"include": "#constant_deprecated_color"}, {"include": "#property_name"}, {"include": "#constant_mathematical_symbols"}, {"include": "#operators"}]}, "rules": {"patterns": [{"include": "#general"}, {"include": "#at_rule_extend"}, {"include": "#at_rule_content"}, {"include": "#at_rule_include"}, {"include": "#at_rule_media"}, {"include": "#selectors"}]}, "selector_attribute": {"captures": {"1": {"name": "punctuation.definition.attribute-selector.begin.bracket.square.scss"}, "2": {"name": "entity.other.attribute-name.attribute.scss"}, "3": {"name": "punctuation.separator.operator.scss"}, "4": {"name": "string.unquoted.attribute-value.scss"}, "5": {"name": "string.quoted.double.attribute-value.scss"}, "6": {"name": "punctuation.definition.string.begin.scss"}, "7": {"name": "punctuation.definition.string.end.scss"}, "8": {"name": "punctuation.definition.attribute-selector.end.bracket.square.scss"}}, "match": "(?i)(\\[)\\s*(-?[_a-z\\\\[[:^ascii:]]][-_a-z0-9\\\\[[:^ascii:]]]*)(?:\\s*([~|^$*]?=)\\s*(?:(-?[_a-z\\\\[[:^ascii:]]][-_a-z0-9\\\\[[:^ascii:]]]*)|((?>(['\"])(?:[^\\\\]|\\\\.)*?(\\6)))))?\\s*(])", "name": "meta.attribute-selector.scss"}, "selector_class": {"begin": "(\\.)(?=[\\w-]|#{)", "beginCaptures": {"1": {"name": "punctuation.definition.entity.css"}}, "end": "(?![\\w-]|(#{))", "name": "entity.other.attribute-name.class.css", "patterns": [{"include": "#interpolation"}]}, "selector_entities": {"match": "\\b(a|abbr|acronym|address|area|article|aside|audio|b|base|bdi|bdo|big|blockquote|body|br|button|canvas|caption|circle|cite|code|col|colgroup|data|datalist|dd|del|details|dfn|dialog|div|dl|dt|ellipse|em|embed|eventsource|fieldset|figure|figcaption|footer|form|frame|frameset|g|(h[1-6])|head|header|hgroup|hr|html|i|iframe|img|image|input|ins|kbd|keygen|label|legend|li|line(?!-)|link|main|map|mark|menu|menuitem|meta|meter|nav|noframes|noscript|object(?!-)|ol|optgroup|option|output|p|param|path|picture|polygon|polyline|pre|progress|q|rb|rect|rp|rt|rtc|ruby|s|samp|script|section|select|small|source|span|strike|strong|style|sub|summary|sup|svg|table(?!-)|tbody|td|template|text(?!-)|textarea|textpath|tfoot|th|thead|time|title|tr|track|tspan|tt|u|ul|var|video|wbr)\\b", "name": "entity.name.tag.scss"}, "selector_custom": {"match": "\\b([a-zA-Z0-9]+(-[a-zA-Z0-9]+)+)(?=\\.|\\s++[^:]|\\s*[,{]|:(link|visited|hover|active|focus|target|lang|disabled|enabled|checked|indeterminate|root|nth-(child|last-child|of-type|last-of-type)|first-child|last-child|first-of-type|last-of-type|only-child|only-of-type|empty|not|valid|invalid)(\\([0-9A-Za-z]*\\))?)", "name": "entity.name.tag.custom.scss"}, "selector_id": {"captures": {"1": {"name": "punctuation.definition.entity.css"}}, "match": "(#)[a-zA-Z][a-zA-Z0-9_-]*", "name": "entity.other.attribute-name.id.css"}, "selector_placeholder": {"captures": {"1": {"name": "punctuation.definition.entity.scss"}}, "match": "(%)[a-zA-Z0-9_-]+", "name": "entity.other.attribute-name.placeholder.scss"}, "parent_selector_suffix": {"match": "(?<=&)[a-zA-Z0-9_-]+", "name": "entity.other.attribute-name.parent-selector-suffix.scss"}, "selector_pseudo_class": {"patterns": [{"begin": "((:)\\bnth-(?:child|last-child|of-type|last-of-type))(\\()", "beginCaptures": {"1": {"name": "entity.other.attribute-name.pseudo-class.css"}, "2": {"name": "punctuation.definition.entity.css"}, "3": {"name": "punctuation.definition.pseudo-class.begin.bracket.round.css"}}, "end": "\\)", "endCaptures": {"0": {"name": "punctuation.definition.pseudo-class.end.bracket.round.css"}}, "patterns": [{"match": "\\d+", "name": "constant.numeric.scss"}, {"match": "(?<=\\d)n\\b|\\b(n|even|odd)\\b", "name": "constant.other.scss"}, {"match": "\\w+", "name": "invalid.illegal.scss"}]}, {"match": "(?x)\n(:)\\b\n(link|visited|hover|active|focus|target|lang|disabled|enabled|checked|\nindeterminate|root|first-child|last-child|first-of-type|last-of-type|\nonly-child|only-of-type|empty|not|valid|invalid)\\b", "captures": {"0": {"name": "entity.other.attribute-name.pseudo-class.css"}, "1": {"name": "punctuation.definition.entity.css"}}}]}, "selector_pseudo_element": {"captures": {"1": {"name": "punctuation.definition.entity.css"}}, "match": "(:+)((-(moz|webkit|ms)-)?(after|before|first-letter|first-line|selection))\\b", "name": "entity.other.attribute-name.pseudo-element.css"}, "selectors": {"comment": "Stuff for Selectors.", "patterns": [{"include": "#selector_entities"}, {"include": "#selector_custom"}, {"include": "#selector_class"}, {"include": "#selector_id"}, {"include": "#selector_pseudo_class"}, {"include": "#tag_wildcard"}, {"include": "#tag_parent_reference"}, {"include": "#selector_pseudo_element"}, {"include": "#selector_attribute"}, {"include": "#selector_placeholder"}, {"include": "#parent_selector_suffix"}]}, "string_double": {"begin": "\"", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.scss"}}, "end": "\"", "endCaptures": {"0": {"name": "punctuation.definition.string.end.scss"}}, "name": "string.quoted.double.scss", "patterns": [{"match": "\\\\(\\h{1,6}|.)", "name": "constant.character.escape.scss"}, {"include": "#interpolation"}]}, "string_single": {"begin": "'", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.scss"}}, "end": "'", "endCaptures": {"0": {"name": "punctuation.definition.string.end.scss"}}, "name": "string.quoted.single.scss", "patterns": [{"match": "\\\\(\\h{1,6}|.)", "name": "constant.character.escape.scss"}, {"include": "#interpolation"}]}, "tag_parent_reference": {"match": "&", "name": "entity.name.tag.reference.scss"}, "tag_wildcard": {"match": "\\*", "name": "entity.name.tag.wildcard.scss"}, "variable": {"patterns": [{"include": "#variables"}, {"include": "#interpolation"}]}, "variable_setting": {"begin": "\\s*(\\$[A-Za-z0-9_-]+\\b)\\s*(:)\\s*", "captures": {"1": {"name": "variable.scss"}, "2": {"name": "punctuation.separator.key-value.scss"}}, "end": "\\s*(?=;)", "name": "meta.set.variable.scss", "patterns": [{"include": "#comment_block"}, {"include": "#comment_line"}, {"include": "#map"}, {"include": "#property_values"}, {"include": "#variable"}]}, "variables": {"match": "\\$[A-Za-z0-9_-]+\\b", "name": "variable.scss"}}, "version": "https://github.com/atom/language-sass/commit/b0417d1412a9169562f637133099fe2bb841a735"}