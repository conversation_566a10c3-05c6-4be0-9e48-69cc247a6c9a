{"name": "@ali/aimi-web", "version": "0.0.22", "description": "AI Web", "files": ["dist"], "scripts": {"start": "ice start --speedup --https=self-signed -p 8001 -h pre-aimi.alibaba-inc.com", "build": "ice build --speedup", "prepublishOnly": "npm run build"}, "keywords": ["ice", "react", "component"], "dependencies": {"@ali/aes-tracker": "^3.3.9", "@ali/aes-tracker-plugin-animFluency": "^3.0.0", "@ali/aes-tracker-plugin-api": "^3.1.3", "@ali/aes-tracker-plugin-autolog": "^3.0.11", "@ali/aes-tracker-plugin-blank": "^3.0.1", "@ali/aes-tracker-plugin-emogine": "^3.0.18", "@ali/aes-tracker-plugin-event": "^3.0.0", "@ali/aes-tracker-plugin-eventTiming": "^3.0.0", "@ali/aes-tracker-plugin-jserror": "^3.0.3", "@ali/aes-tracker-plugin-longtask": "^3.0.1", "@ali/aes-tracker-plugin-perf": "^3.1.0", "@ali/aes-tracker-plugin-pv": "^3.0.6", "@ali/aes-tracker-plugin-resourceError": "^3.0.4", "@ali/click-to-react-component": "^1.1.3", "@ali/ice-plugin-spm": "^2.1.0", "@ali/mc-icons": "workspace:^", "@ali/mc-request": "workspace:^", "@ali/mc-services": "workspace:^", "@ali/mc-uikit": "workspace:^", "@ali/mtl-themes": "workspace:^", "@ant-design/pro-chat": "^1.15.3", "@ant-design/x": "^1.1.0", "@ice/jsx-runtime": "0.3.0-canary-3ce96e348-20240507093654", "@ice/plugin-icestark": "^1.1.1", "@ice/runtime": "^1.4.11", "@ice/stark": "^2.7.5", "@ice/stark-app": "^1.5.0", "@ice/stark-data": "^0.1.3", "@microlink/react-json-view": "^1.26.1", "@types/lodash-es": "^4.17.12", "ahooks": "^3.7.8", "ai": "3.4.33", "antd": "^5.23.0", "classnames": "^2.5.1", "ignore": "^7.0.4", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "rc-queue-anim": "^2.0.0", "rc-tween-one": "^3.0.6", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^9.0.1", "react-router-dom": "^6.23.1", "react-syntax-highlighter": "^15.6.1", "rehype-external-links": "^3.0.0", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "uri-js": "^4.4.1", "uuid": "^11.1.0", "zod": "^3.24.4"}, "devDependencies": {"@continuedev/config-yaml": "1.0.86", "@ice/app": "3.4.11", "@ice/route-manifest": "^1.3.0", "@types/js-cookie": "^3.0.6", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "yargs-parser": "^21.1.1"}, "peerDependencies": {"react": "^17 || ^18"}, "publishConfig": {"registry": "https://registry.anpm.alibaba-inc.com"}, "license": "MIT"}