import React, { useEffect, useRef, useState, useMemo } from 'react';
import { Flex, Button, Avatar, Drawer, theme, Spin, List } from 'antd';
import AimiChat from '@/components/AimiChat';
import {
  ArrowRightOutlined,
  ArrowUpRightOutlined,
  CodeOutlined,
  PlusOutlined,
  ExploreOutlined,
  SecretBookFilled,
} from '@ali/mc-icons';
import { pageAgent } from '@ali/mc-services/AiMiAgent';
import { getDashboardStatistics } from '@ali/mc-services/AiMiDashboard';
import { all, facade } from '@ali/mc-services/AiMiAgentConversationSample';
import { StatusTag } from '@ali/mc-uikit';
import { defineDataLoader, useNavigate, useSearchParams } from 'ice';
import { getMcpServerMetas, GetMcpServerMetasParams } from '@ali/mc-services/AiMiMcpServerMeta';
import { useDeferData } from '@/hooks/useDeferData';
import { PageMcpServerMetaVO } from '@ali/mc-services/AiMiMcpDataContracts';
import {
  AgentConversationSampleVO,
  DashboardStatisticsVO,
  PageKnowledgeBaseVO,
  AgentConversationSampleGroupVO,
  AgentVO,
} from '@ali/mc-services/AiMiDataContracts';
import AimiChatImage from '@/assets/aimi-chat.gif';
import SessionHistory from '@/components/AimiChat/SessionHistory';
import { pageKnowledgeBase } from '@ali/mc-services/AiMiKnowledgeBase';
import classNames from 'classnames';
import McpServerCard from './components/McpServerCard';
import KnowledgeCard from './components/KnowledgeCard';
import SdkCard from './components/SdkCard';
import AgentCard from './components/AgentCard';
import SampleCard from './components/SampleCard';
import AniButton from './components/AniButton';
import { RightPanelType } from './components/type';
import RightPanelHeader from './components/RightPanelHeader';
import SecretBookCard from './components/SecretBookCard';
import styles from './index.module.less';
import { useRequest } from '@ali/mc-request';

/** 最小可适应区域的内容区域高度 */
const MIN_CONTENT_HEIGHT = 72.5 + 64;

const STATISTICS_MAP: { [key: string]: string } = {
  knowledgeBaseCount: '知识库',
  mcpToolCount: 'MCP Tool',
  sdkCount: 'SDK强化学习',
  codeFileCount: '代码文件',
};

const DEFAULT_AGENTS: AgentVO[] = [
  { name: 'AIMI-Agent', identifier: 'aimi-main', latestInstanceIdentifier: 'aimi-main' },
];

export default function Dashboard() {
  const { token } = theme.useToken();

  const [searchParams, setSearchParams] = useSearchParams();
  const [showWelcome, setShowWelcome] = useState(true);
  const [showSessionHistory, setShowSessionHistory] = useState(false);
  const [senderValue, setSenderValue] = useState<string | undefined>(undefined);
  const [contentHeight, setContentHeight] = useState(0);
  const [sampleWrapperHeight, setSampleWrapperHeight] = useState(0);
  const [itemCardWidth, setItemCardWidth] = useState(0);
  const agentIdentifier = searchParams.get('agentIdentifier') || 'aimi-main';
  const sessionId = Number(searchParams.get('sessionId'));
  const aimiChatRef = useRef<any>();
  const senderRef = useRef<any>();
  const contentRef = useRef<any>();
  const itemCardRef = useRef<any>();
  const navigate = useNavigate();

  const [rightPanelType, setRightPanelType] = useState<RightPanelType>();

  useEffect(() => {
    if (sessionId) setShowWelcome(false);
  }, [sessionId]);

  const { data, loading = true } = useDeferData<{
    dashboardStatistics: DashboardStatisticsVO;
    knowledgeBaseRes: PageKnowledgeBaseVO;
    agentList: AgentVO[];
    allSample: AgentConversationSampleGroupVO[];
    facadeSample: AgentConversationSampleVO[];
  }>();

  const { runAsync: getMcpServerList, data: mcpServerRes } = useRequest<
    PageMcpServerMetaVO,
    [GetMcpServerMetasParams | undefined]
  >(getMcpServerMetas);

  useEffect(() => {
    getMcpServerList({
      pageNo: 0,
      pageSize: 10,
    });
  }, []);

  const updateSessionId = (value?: number) => {
    if (value) {
      setSearchParams((prevParams) => {
        prevParams.set('sessionId', String(value));
        return prevParams;
      });
    } else {
      setSearchParams((prevParams) => {
        prevParams.delete('sessionId');
        return prevParams;
      });
    }
  };

  const changeAgent = (newAgentIdentifier?: string) => {
    setSearchParams((prevParams) => {
      newAgentIdentifier && prevParams.set('agentIdentifier', newAgentIdentifier);
      prevParams.delete('sessionId');
      return prevParams;
    });
    setSenderValue(undefined);
    aimiChatRef.current?.clearMessage();
    setShowWelcome(newAgentIdentifier === 'aimi-main');
  };

  const recentAgents = useMemo(() => {
    return (data?.agentList?.items ?? [])?.filter((item) => item?.latestInstanceIdentifier);
  }, [data?.agentList]);

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      const { height } = entries[0].contentRect;
      let newSampleWrapperHeight = 0;

      const SAMPLE_HEIGHT = 40;
      const SAMPLE_GAP = 12;
      const rawSampleHeight = height - MIN_CONTENT_HEIGHT - 24;
      // 当高度不足展示一行时，不展示
      if (rawSampleHeight < SAMPLE_HEIGHT) {
        newSampleWrapperHeight = 0;
        // 当高度仅足展示一行时，展示一行
      } else if (rawSampleHeight < SAMPLE_HEIGHT * 2 + SAMPLE_GAP) {
        newSampleWrapperHeight = SAMPLE_HEIGHT;
        // 当高度可展示两行时，展示两行
      } else {
        newSampleWrapperHeight = SAMPLE_HEIGHT * 2 + SAMPLE_GAP;
      }
      setContentHeight(height);
      setSampleWrapperHeight(newSampleWrapperHeight);
    });

    if (contentRef.current) {
      observer.observe(contentRef.current);
    }

    return () => observer.disconnect();
  }, [showWelcome]);

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      const { width } = entries[0].contentRect;
      setItemCardWidth(width);
    });

    if (itemCardRef.current) {
      observer.observe(itemCardRef.current);
    }

    return () => observer.disconnect();
  }, [showWelcome]);

  return (
    <Flex className={styles.dashboardContainer} justify="center">
      <Flex
        justify="center"
        vertical
        className={classNames({
          [styles.leftPanel]: true,
          [styles.rightPanelVisible]: rightPanelType?.length,
        })}
      >
        <Flex style={{ marginBottom: token.marginMD, width: '100%' }} align="center" justify="space-between">
          <Flex style={{ overflow: 'scroll' }}>
            <Flex gap={token.marginXS}>
              {DEFAULT_AGENTS?.concat(recentAgents)
                ?.filter((item) => item?.latestInstanceIdentifier)
                ?.splice(0, 3)
                ?.map((item) => (
                  <Button
                    key={item?.latestInstanceIdentifier}
                    onClick={() => changeAgent(item?.latestInstanceIdentifier)}
                    className={classNames({
                      [styles.agentBtn]: true,
                      [styles.agentBtnActive]: agentIdentifier === item?.latestInstanceIdentifier,
                    })}
                  >
                    <Flex justify="space-between" align="center" gap={token.margin}>
                      <span className={styles.name}>{item.name}</span>
                      <StatusTag>{item?.identifier === 'aimi-main' ? '官方' : '自定义'}</StatusTag>
                    </Flex>
                  </Button>
                ))}
            </Flex>
          </Flex>
          <Button type="text" onClick={() => setRightPanelType('agent')}>
            <ExploreOutlined />
            探索更多Agent
          </Button>
        </Flex>
        <Flex align="center" vertical className={styles.dashboardCard}>
          {showWelcome && (
            <>
              <Flex className={styles.dashboardCardHeader} vertical align="center">
                <List
                  ref={itemCardRef}
                  style={{ width: '100%' }}
                  grid={{ gutter: 16, column: itemCardWidth > 860 ? 4 : 2 }}
                  // TODO: 后端休假去了，前端先写死
                  dataSource={Object.entries(
                    data?.dashboardStatistics
                      ? { ...data?.dashboardStatistics, sdkCount: 40 }
                      : {
                          knowledgeBaseCount: '',
                          mcpToolCount: '',
                          sdkCount: '',
                          codeFileCount: '',
                        },
                  )?.slice(0, 4)}
                  renderItem={([key, value]) => (
                    <List.Item>
                      <Flex
                        key={key}
                        className={styles.itemCard}
                        justify="space-between"
                        align="center"
                        style={{
                          cursor: key === 'codeFileCount' ? 'default' : 'pointer',
                        }}
                        onClick={() => {
                          if (key !== 'codeFileCount') {
                            setRightPanelType(key as any);
                          }
                        }}
                      >
                        <Flex gap={token.marginXS} align="center">
                          <Flex className={styles.itemCardCount} align="center" gap={token.marginXXS}>
                            {value}
                            <PlusOutlined style={{ fontSize: token.fontSizeLG }} />
                          </Flex>
                          <div className={styles.itemCardName}>{STATISTICS_MAP[key]}</div>
                        </Flex>
                        {key !== 'codeFileCount' && <ArrowUpRightOutlined className={styles.itemCardIcon} />}
                      </Flex>
                    </List.Item>
                  )}
                />
              </Flex>
              <Flex className={styles.decs} gap={token.margin} align="center" style={{ marginTop: token.margin }}>
                <span>我已集成了终端研发海量知识库 X 标准化MCP工具</span>
                <AniButton />
              </Flex>
              {/* </Flex> */}
              <Flex vertical align="center" justify="center" className={styles.contentWrapper} ref={contentRef}>
                <Flex className={styles.helloMsgWrapper} align="center" vertical gap={token.margin}>
                  <Avatar size={32} src={AimiChatImage} shape="circle" />
                  <div className={styles.helloMsg}>Hi，我是AIMI</div>
                </Flex>
                {/* 空间充足才展示 */}
                {sampleWrapperHeight > 0 && (
                  <Flex
                    className={styles.samples}
                    style={{ maxHeight: sampleWrapperHeight }}
                    gap={token.marginSM}
                    justify="start"
                  >
                    {data?.facadeSample?.map((item) => (
                      <Button
                        key={item?.id}
                        className={styles.questionButton}
                        onClick={() => {
                          aimiChatRef.current?.sendMessage(item?.question);
                          setSenderValue(undefined);
                        }}
                      >
                        <CodeOutlined style={{ marginRight: token.marginXS }} />
                        {item?.question}
                      </Button>
                    ))}
                  </Flex>
                )}
                {/* 空间充足才展示 */}
                {contentHeight - sampleWrapperHeight > MIN_CONTENT_HEIGHT && (
                  <Flex className={styles.moreSampleBtn} style={{ marginTop: token.marginLG }}>
                    <Button className={styles.questionListButton} onClick={() => setRightPanelType('sample')}>
                      更多会话示例 <ArrowRightOutlined />
                    </Button>
                  </Flex>
                )}
              </Flex>
            </>
          )}
          <Drawer open={showSessionHistory} placement="left" onClose={() => setShowSessionHistory(false)}>
            <SessionHistory
              showCloseIcon={false}
              agentIdentifier={agentIdentifier}
              autoSelectFirstItem={false}
              // onActiveKeyChange={(key) => {
              //   setShowSessionHistory(false);
              //   updateSessionId(key);
              // }}
              onActiveKeyChange={(key) =>
                navigate(`/chat?agentIdentifier=${agentIdentifier}&sessionId=${key}&from=dashboard`)
              }
            />
          </Drawer>
          <AimiChat
            // className={showWelcome ? styles.dashboardAimiChatWithWelcome : styles.dashboardAimiChat}
            className={classNames({
              [styles.dashboardAimiChat]: true,
              [styles.dashboardAimiChatWithWelcome]: showWelcome,
            })}
            // autoHeight
            autoHeight={!showWelcome}
            agentIdentifier={agentIdentifier}
            autoSelectFirstItem={false}
            defaultNewSession={!sessionId}
            sessionId={sessionId}
            updateSessionId={updateSessionId}
            proChatConfig={{
              chatRef: aimiChatRef,
              actionConfig: {
                showNewSessionAction: !!sessionId,
                onHistoryClick: () => {
                  setShowSessionHistory(true);
                },
                onNewSessionClick: () => setShowWelcome(agentIdentifier === 'aimi-main'),
              },
              aimiSenderConfig: {
                senderRef: senderRef,
                autoSize: { minRows: 2, maxRows: 2 },
                value: senderValue,
                // onChange: (value: string) => {
                //   setSenderValue(value);
                // },
                afterSubmit: () => setSenderValue(undefined),
                actions:
                  agentIdentifier === 'aimi-main'
                    ? [
                      <Button
                        key="explore"
                        type="primary"
                        className={styles.chatExploreBtn}
                        onClick={() => setRightPanelType('secretBook')}
                      >
                        <SecretBookFilled />
                        探索
                      </Button>,
                      ]
                    : [],
              },
              onRequest: () => setShowWelcome(false),
            }}
          />
        </Flex>
      </Flex>
      <Flex
        className={classNames({
          [styles.rightPanel]: true,
          [styles.visible]:
            rightPanelType?.length &&
            !(rightPanelType === 'sample' && agentIdentifier !== 'aimi-main') &&
            !(rightPanelType === 'secretBook' && agentIdentifier !== 'aimi-main'),
        })}
        vertical
      >
        <Flex
          vertical
          gap={token.marginLG}
          style={{
            height: '100%',
            width: 'calc(40vw - 96px)',
            margin: token.marginLG,
            marginTop: 0,
          }}
        >
          {rightPanelType && (
            <RightPanelHeader rightPanelType={rightPanelType} onClose={() => setRightPanelType(undefined)} />
          )}
          <Spin wrapperClassName={styles.spinContainer} style={{ height: '100%' }} spinning={loading}>
            {rightPanelType === 'mcpToolCount' && <McpServerCard initData={mcpServerRes} />}
            {rightPanelType === 'knowledgeBaseCount' && <KnowledgeCard initData={data?.knowledgeBaseRes} />}
            {rightPanelType === 'sdkCount' && <SdkCard />}
            {rightPanelType === 'agent' && <AgentCard initData={data?.agentList} />}
            {rightPanelType === 'sample' && (
              <SampleCard
                initData={data?.allSample}
                onSendMessage={(question: string) => {
                  updateSessionId();
                  aimiChatRef.current?.clearMessage();
                  aimiChatRef.current?.sendMessage(question);
                  setSenderValue(undefined);
                }}
              />
            )}
            {rightPanelType === 'secretBook' && (
              <SecretBookCard
                onClick={(question) => {
                  setSenderValue(question);
                  senderRef.current?.focus();
                }}
              />
            )}
          </Spin>
        </Flex>
      </Flex>
    </Flex>
  );
}

export const dataLoader = defineDataLoader(
  async () => {
    const [knowledgeBaseRes, agentList, dashboardStatistics, allSample, facadeSample] = await Promise.all(
      [
        pageKnowledgeBase({
          pageNo: 0,
          pageSize: 10,
        } as any),
        pageAgent({
          pageNo: 0,
          pageSize: 10,
          status: 'ONLINE',
        }),
        getDashboardStatistics(),
        all({
          agentIdentifier: 'aimi-main',
        }),
        facade({
          agentIdentifier: 'aimi-main',
        }),
      ],
    );
    return {
      agentList,
      dashboardStatistics,
      knowledgeBaseRes,
      allSample,
      facadeSample,
    };
  },
  { defer: true },
);
