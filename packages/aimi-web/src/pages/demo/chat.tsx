import React from 'react';
import { useAppData, useSearchParams } from 'ice';

import Chat from '@ali/mc-uikit/Chat';
import { Button } from 'antd';
import { createSession } from '@ali/mc-services/AiAgentApi';
import { AgentSessionCreateDTO } from '@ali/mc-services/AiDataContracts';
import { useRequest } from '@ali/mc-request';

export default function ChatDemo() {
  const [searchParams, setSearchParams ] = useSearchParams();
  const sessionId = Number(searchParams.get('sessionId'));
  const { user } = useAppData() || {};

  const { runAsync: requestCreateSession } = useRequest<number, [AgentSessionCreateDTO]>(createSession);

  const handleCreateSession = () => {
    if (!user) return;

    requestCreateSession({
      empId: user?.empId,
      chatType: 'AIMI',
      platform: 'WEB',
      agentIdentifier: 'aimi-main',
    }).then((newSessionId) => {
      setSearchParams({
        sessionId: newSessionId.toString(),
      })
    })
  };

  return (
    <Chat
      defaultSessionId={sessionId}
      agentIdentifier="aimi-main"
      maxHeight="75vh"
      senderBeforeAddon={<Button onClick={handleCreateSession}>开启新会话</Button>}
    />
  );
}
