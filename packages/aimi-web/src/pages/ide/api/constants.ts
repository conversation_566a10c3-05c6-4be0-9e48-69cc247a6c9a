// To IDE

import { ToIdeProtocol } from '@/components/IdeProtocol';

// Webview / Core -> IDE
export const toIdeFromWebviewOrCoreMessageList = [
  'getIdeInfo',
  'getWorkspaceDirs',
  'writeFile',
  'showVirtualFile',
  'openFile',
  'openUrl',
  'runCommand',
  'getSearchResults',
  'getFileResults',
  'subprocess',
  'saveFile',
  'fileExists',
  'readFile',
  'diffLine',
  'getProblems',
  'getOpenFiles',
  'getCurrentFile',
  'getPinnedFiles',
  'showLines',
  'readRangeInFile',
  'getDiff',
  'getWorkspaceConfigs',
  'getTerminalContents',
  'getDebugLocals',
  'getTopLevelCallStackSources',
  'getAvailableThreads',
  'isTelemetryEnabled',
  'getUniqueId',
  'getTags',
  'readSecrets',
  'writeSecrets',
  'getIdeSettings',
  'getBranch',
  'getRepoName',
  'showToast',
  'getGitRootPath',
  'listDir',
  'getFileStats',
  'gotoDefinition',
  'getGitHubAuthToken',
  'getControlPlaneSessionInfo',
  'logoutOfControlPlane',
  'reportError',
  'closeSidebar',
];

// Webview  -> IDE
export const toIdeFromWebviewMessageList = [
  ...toIdeFromWebviewOrCoreMessageList,
  'openUrl',
  'applyToFile',
  'overwriteFile',
  'showTutorial',
  'showFile',
  'toggleDevTools',
  'reloadWindow',
  'focusEditor',
  'toggleFullScreen',
  'insertAtCursor',
  'copyText',
  'jetbrains/isOSREnabled',
  'jetbrains/onLoad',
  'jetbrains/getColors',
  'vscode/openMoveRightMarkdown',
  'setGitHubAuthToken',
  'acceptDiff',
  'rejectDiff',
  'edit/sendPrompt',
  'edit/addCurrentSelection',
  'edit/clearDecorations',
];

// Core -> IDE
export const toIdeFromCoreMessageList = toIdeFromWebviewOrCoreMessageList;

// 发给IDE的消息列表
export const toIdeMessageList = Array.from(new Set([...toIdeFromCoreMessageList, ...toIdeFromWebviewMessageList]));

// To Core
// IDE -> Webview / Core
export const toWebviewOrCoreFromIdeMessageList = ['didChangeActiveTextEditor'];

// IDE / Webview ->  Core
export const toCoreFromIdeOrWebviewMessageList = [
  'ping',
  'abort',
  'history/list',
  'history/delete',
  'history/load',
  'history/save',
  'history/clear',
  'devdata/log',
  'config/addOpenAiKey',
  'config/addModel',
  'config/addLocalWorkspaceBlock',
  'config/newPromptFile',
  'config/ideSettingsUpdate',
  'config/getSerializedProfileInfo',
  'config/deleteModel',
  'config/reload',
  'config/refreshProfiles',
  'config/openProfile',
  'config/updateSharedConfig',
  'config/updateSelectedModel',
  'context/getContextItems',
  'mcp/reloadServer',
  'context/getSymbolsForFiles',
  'context/loadSubmenuItems',
  'autocomplete/complete',
  'context/addDocs',
  'context/removeDocs',
  'context/indexDocs',
  'autocomplete/cancel',
  'autocomplete/accept',
  'llm/complete',
  'llm/listModels',
  'llm/streamChat',
  'streamDiffLines',
  'chatDescriber/describe',
  'stats/getTokensPerDay',
  'stats/getTokensPerModel',
  'tts/kill',
  'index/setPaused',
  'index/forceReIndex',
  'index/indexingProgressBarInitialized',
  'completeOnboarding',
  'files/changed',
  'files/opened',
  'files/created',
  'files/deleted',
  'files/closed',
  'indexing/reindex',
  'indexing/abort',
  'indexing/setPaused',
  'docs/getSuggestedDocs',
  'docs/initStatuses',
  'docs/getDetails',
  'addAutocompleteModel',
  'auth/getAuthUrl',
  'tools/call',
  'clipboardCache/add',
  'controlPlane/openUrl',
  'isItemTooBig',
  'didChangeControlPlaneSessionInfo',
  'process/markAsBackgrounded',
  'process/isBackgrounded',
];

// IDE -> Core
export const toCoreFromIdeMessageList = [...toCoreFromIdeOrWebviewMessageList, ...toWebviewOrCoreFromIdeMessageList];

// Webview ->  Core
export const toCoreFromWebviewMessageList = [
  ...toCoreFromIdeOrWebviewMessageList,
  'didChangeSelectedProfile',
  'didChangeSelectedOrg',
];

// 发给Core的消息列表
export const toCoreMessageList = Array.from(new Set([...toCoreFromIdeMessageList, ...toCoreFromWebviewMessageList]));

// To Webview
// IDE / Core -> Webview
export const toWebviewFromIdeOrCoreMessageList = [
  'configUpdate',
  'getDefaultModelTitle',
  'indexProgress',
  'indexing/statusUpdate',
  'refreshSubmenuItems',
  'didCloseFiles',
  'isContinueInputFocused',
  'addContextItem',
  'setTTSActive',
  'getWebviewHistoryLength',
  'getCurrentSessionId',
  'jetbrains/setColors',
  'sessionUpdate',
  'toolCallPartialOutput',
];

// IDE -> Webview
export const toWebviewFromIdeMessageList = [
  ...toWebviewFromIdeOrCoreMessageList,
  'setInactive',
  'submitMessage',
  'newSessionWithPrompt',
  'userInput',
  'focusContinueInput',
  'focusContinueInputWithoutClear',
  'focusContinueInputWithNewSession',
  'highlightedCode',
  'setCodeToEdit',
  'navigateTo',
  'addModel',
  'focusContinueSessionId',
  'newSession',
  'setTheme',
  'setColors',
  'jetbrains/editorInsetRefresh',
  'jetbrains/isOSREnabled',
  'addApiKey',
  'setupLocalConfig',
  'incrementFtc',
  'openOnboardingCard',
  'applyCodeFromChat',
  'updateApplyState',
  'exitEditMode',
  'focusEdit',
  ...toWebviewOrCoreFromIdeMessageList,
];

// Core -> Webview
export const toWebviewFromCoreMessageList = [...toWebviewFromIdeOrCoreMessageList];

// 发给Webview的消息列表
export const toWebViewMessageList = Array.from(
  new Set([...toWebviewFromIdeMessageList, ...toWebviewFromCoreMessageList]),
);

export const transType = (obj: any) => {
  let res: any;
  Object.keys(obj).forEach((key) => {
    res.push({
      name: key,
      input: obj[key][0],
      output: obj[key][1],
    });
  });
  return res;
};

export const toIdeFromWebviewOrCoreTypeMap: { [key in keyof ToIdeProtocol]: any[] } = {
  // Methods from IDE type
  getIdeInfo: [undefined, 'IdeInfo'],
  getWorkspaceDirs: [undefined, 'string[]'],
  writeFile: [{ path: 'string', contents: 'string' }, 'void'],
  showVirtualFile: [{ name: 'string', content: 'string' }, 'void'],
  openFile: [{ path: 'string' }, 'void'],
  openUrl: ['string', 'void'],
  runCommand: [{ command: 'string', 'options?': 'TerminalOptions' }, 'string'],
  getSearchResults: [{ query: 'string' }, 'string'],
  getFileResults: [{ pattern: 'string' }, 'string[]'],
  subprocess: [{ command: 'string', 'cwd?': 'string' }, ['string', 'string']],
  saveFile: [{ filepath: 'string' }, 'void'],
  fileExists: [{ filepath: 'string' }, 'boolean'],
  readFile: [{ filepath: 'string' }, 'string'],
  diffLine: [
    {
      diffLine: 'DiffLine',
      filepath: 'string',
      startLine: 'number',
      endLine: 'number',
    },
    'void',
  ],
  getProblems: [{ filepath: 'string' }, 'Problem[]'],
  getOpenFiles: [undefined, 'string[]'],
  getCurrentFile: [undefined, 'any'],
  getPinnedFiles: [undefined, 'string[]'],
  getClipboardContent: [undefined, '{ text: string; copiedAt: number; range?: Range; filepath?: string; }'],
  showLines: [{ filepath: 'string', startLine: 'number', endLine: 'number' }, 'void'],
  readRangeInFile: [{ filepath: 'string', range: 'Range' }, 'string'],
  getDiff: [{ includeUnstaged: 'boolean' }, 'string[]'],
  getWorkspaceConfigs: [undefined, 'ContinueRcJson[]'],
  getTerminalContents: [undefined, 'string'],
  getDebugLocals: [{ threadIndex: 'number' }, 'string'],
  getTopLevelCallStackSources: [{ threadIndex: 'number', stackDepth: 'number' }, 'string[]'],
  getAvailableThreads: [undefined, 'Thread[]'],
  isTelemetryEnabled: [undefined, 'boolean'],
  getUniqueId: [undefined, 'string'],
  getTags: ['string', 'IndexTag[]'],
  readSecrets: [{ keys: 'string[]' }, 'Record<string, string>'],
  writeSecrets: [{ secrets: 'Record<string, string>' }, 'void'],
  // end methods from IDE type

  getIdeSettings: [undefined, 'IdeSettings'],
  // Git
  getBranch: [{ dir: 'string' }, 'string'],
  getRepoName: [{ dir: 'string' }, 'string | undefined'],
  showToast: ['Parameters<IDE[showToast]>', 'Awaited<ReturnType<IDE[showToast]>>'],
  getGitRootPath: [{ dir: 'string' }, 'string | undefined'],
  listDir: [{ dir: 'string' }, '[string, FileType][]'],
  getFileStats: [{ files: 'string[]' }, 'FileStatsMap'],

  gotoDefinition: [{ location: 'Location' }, 'RangeInFile[]'],

  getGitHubAuthToken: ['GetGhTokenArgs', 'string | undefined'],
  getControlPlaneSessionInfo: [{ silent: 'boolean', useOnboarding: 'boolean' }, 'ControlPlaneSessionInfo | undefined'],
  logoutOfControlPlane: [undefined, 'void'],
  reportError: ['any', 'void'],
  closeSidebar: [undefined, 'void'],
  applyToFile: [
    {
      text: 'string',
      streamId: 'string',
      'filepath?': 'string',
      'toolCallId?': 'string',
    },
    'void',
  ],
  overwriteFile: [{ filepath: 'string', prevFileContent: 'string | null' }, 'void'],
  showTutorial: [undefined, 'void'],
  showFile: [{ filepath: 'string' }, 'void'],
  toggleDevTools: [undefined, 'void'],
  reloadWindow: [undefined, 'void'],
  focusEditor: [undefined, 'void'],
  toggleFullScreen: ['{ newWindow?: boolean } | undefined', 'void'],
  insertAtCursor: [{ text: 'string' }, 'void'],
  copyText: [{ text: 'string' }, 'void'],
  'jetbrains/isOSREnabled': [undefined, 'boolean'],
  'jetbrains/onLoad': [
    undefined,
    {
      windowId: 'string',
      serverUrl: 'string',
      workspacePaths: 'string[]',
      vscMachineId: 'string',
      vscMediaUrl: 'string',
    },
  ],
  'jetbrains/getColors': [undefined, 'Record<string, string>'],
  'vscode/openMoveRightMarkdown': [undefined, 'void'],
  setGitHubAuthToken: [{ token: 'string' }, 'void'],
  acceptDiff: [{ filepath: 'string', 'streamId?': 'string' }, 'void'],
  rejectDiff: [{ filepath: 'string', 'streamId?': 'string' }, 'void'],
  'edit/sendPrompt': [
    {
      prompt: 'MessageContent',
      range: 'RangeInFileWithContents',
    },
    'string | undefined',
  ],
  'edit/addCurrentSelection': [undefined, 'void'],
  'edit/clearDecorations': [undefined, 'void'],
};
