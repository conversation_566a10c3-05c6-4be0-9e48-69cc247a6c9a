
import { CoreTool } from 'ai';
import { inferResolvedUriFromRelativePath } from '../utils/ideUtils';
import { getCleanUriPath, getUriPathBasename } from '@/components/IdeProtocol/uri';
import { z } from 'zod';

export const createNewFileTool: CoreTool = {
  description:
    "Create a new file. Only use this when a file doesn't exist and should be created",
  parameters: z.object({
    filepath: z.string().describe('The path where the new file should be created, relative to the root of the workspace'),
    contents: z.string().describe('The contents to write to the new file'),
  }),
  execute: async (args, extras: any) => {
    const resolvedFileUri = await inferResolvedUriFromRelativePath(
      args.filepath,
      extras.ide,
    );
    if (resolvedFileUri) {
      const exists = await extras.ide.fileExists(resolvedFileUri);
      if (exists) {
        throw new Error(
          `File ${args.filepath} already exists. Use the edit tool to edit this file`,
        );
      }
      await extras.ide.writeFile(resolvedFileUri, args.contents);
      await extras.ide.openFile(resolvedFileUri);
      return [
        {
          name: getUriPathBasename(resolvedFileUri),
          description: getCleanUriPath(resolvedFileUri),
          content: 'File created successfully',
          uri: {
            type: 'file',
            value: resolvedFileUri,
          },
        },
      ];
    } else {
      throw new Error('Failed to resolve path');
    }
  },
};
