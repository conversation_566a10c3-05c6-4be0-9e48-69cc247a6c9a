
import { CoreTool } from 'ai';
import { z } from 'zod';

export const globSearchTool: CoreTool = {
  description: 'Search for files in the project',
  parameters: z.object({
    pattern: z.string().describe('Glob pattern for file path matching'),
  }),
  execute: async (args, extras: any) => {
    const results = await extras.ide.getFileResults(args.pattern);
    return [
      {
        name: 'File results',
        description: 'Results from file glob search',
        content: results.join('\n'),
      },
    ];
  },
};
