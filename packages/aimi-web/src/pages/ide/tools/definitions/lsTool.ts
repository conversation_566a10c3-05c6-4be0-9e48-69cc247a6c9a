
// import { CoreTool } from 'ai';
// import { resolveRelativePathInDir } from '../utils/ideUtils';
// import { walkDir } from '../utils/walkDir';
// import { z } from 'zod';

// export const lsTool: CoreTool = {
//   description: 'List files and folders in a given directory',
//   parameters: z.object({
//     dirPath: z.string().describe("The directory path relative to the root of the project. Always use forward slash paths like '/'. rather than e.g. '.'"),
//     recursive: z.boolean().optional().describe('If true, lists files and folders recursively. To prevent unexpected large results, use this sparingly'),
//   }),
//   execute: async (args, extras: any) => {
//     const uri = await resolveRelativePathInDir(args.dirPath, extras.ide);
//     if (!uri) {
//       throw new Error(
//         `Directory ${args.dirPath} not found. Make sure to use forward-slash paths. Do not use e.g. "."`,
//       );
//     }

//     const entries = await walkDir(uri, extras.ide, {
//       returnRelativeUrisPaths: true,
//       include: 'both',
//       recursive: args.recursive ?? false,
//     });

//     const content =
//       entries.length > 0
//         ? entries.join('\n')
//         : `No files/folders found in ${args.dirPath}`;

//     return [
//       {
//         name: 'File/folder list',
//         description: `Files/folders in ${args.dirPath}`,
//         content,
//       },
//     ];
//   },
// };
