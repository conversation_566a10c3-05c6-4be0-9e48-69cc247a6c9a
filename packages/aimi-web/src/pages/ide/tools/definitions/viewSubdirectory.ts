// import { CoreTool } from 'ai';
// import { z } from 'zod';

// export const viewSubdirectoryTool: CoreTool = {
//   description: 'View the contents of a subdirectory',
//   parameters: z.object({
//     directory_path: z.string().describe(
//       'The path of the subdirectory to view, relative to the root of the workspace',
//     ),
//   }),
//   // execute: async (args: any, extras: any) => {
//   //   const { directory_path } = args;
//   //   const uri = await resolveRelativePathInDir(directory_path, extras.ide);

//   //   if (!uri) {
//   //     throw new Error(`Directory path "${directory_path}" does not exist.`);
//   //   }

//   //   const repoMap = await generateRepoMap(extras.llm, extras.ide, {
//   //     dirUris: [uri],
//   //     outputRelativeUriPaths: true,
//   //     includeSignatures: false,
//   //   });

//   //   return [
//   //     {
//   //       name: 'Repo map',
//   //       description: `Map of ${directory_path}`,
//   //       content: repoMap,
//   //     },
//   //   ];
//   // },
// };
