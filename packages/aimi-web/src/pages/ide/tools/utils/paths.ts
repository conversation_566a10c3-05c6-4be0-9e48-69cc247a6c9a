// import * as path from 'path';
// import * as fs from 'fs';
// import * as os from 'os';

// const CONTINUE_GLOBAL_DIR = (() => {
//   const configPath = process.env.CONTINUE_GLOBAL_DIR;
//   if (configPath) {
//     // Convert relative path to absolute paths based on current working directory
//     return path.isAbsolute(configPath)
//       ? configPath
//       : path.resolve(process.cwd(), configPath);
//   }
//   return path.join(os.homedir(), '.continue');
// })();

// export function getContinueGlobalPath(): string {
//   // This is ~/.continue on mac/linux
//   const continuePath = CONTINUE_GLOBAL_DIR;
//   if (!fs.existsSync(continuePath)) {
//     fs.mkdirSync(continuePath);
//   }
//   return continuePath;
// }

// export function getGlobalContinueIgnorePath(): string {
//   const continueIgnorePath = path.join(
//     getContinueGlobalPath(),
//     '.continueignore',
//   );
//   if (!fs.existsSync(continueIgnorePath)) {
//     fs.writeFileSync(continueIgnorePath, '');
//   }
//   return continueIgnorePath;
// }


// export function getContinueUtilsPath(): string {
//   const utilsPath = path.join(getContinueGlobalPath(), '.utils');
//   if (!fs.existsSync(utilsPath)) {
//     fs.mkdirSync(utilsPath);
//   }
//   return utilsPath;
// }
// export function getRepoMapFilePath(): string {
//   return path.join(getContinueUtilsPath(), 'repo_map.txt');
// }
