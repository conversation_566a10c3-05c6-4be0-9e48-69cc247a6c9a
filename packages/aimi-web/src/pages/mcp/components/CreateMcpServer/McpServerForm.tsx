import { Flex, Form, FormInstance, Input, Switch, theme } from 'antd';
import React from 'react';
import styles from './index.module.less';
import BizGroupSelector from '@/components/BizGroupSelector';
import McpToolTable from './McpToolTable';
import { Copy } from '@ali/mc-uikit';
import { CopyOutlined } from '@ali/mc-icons';

interface IProps {
  form: FormInstance;
  isUpdate?: boolean;
}
export const defaultServerIcon = 'https://picasso-work.alibaba-inc.com/i1/O1CN01fV4h0l1cqfToSdbOu_!!6000000003652-55-tps_intranet-48-48.svg';

const McpServerForm = (props: IProps) => {
  const { form, isUpdate = false } = props;
  const { token } = theme.useToken();

  return (<Form
    form={form}
    layout="vertical"
    className={styles.mcpServerForm}
  >
    <Form.Item label="名称" name="name" rules={[{ required: true, message: '请输入MCP Server名称' }]}>
      <Input placeholder="请输入MCP Server名称" />
    </Form.Item>
    <Form.Item label="业务标签" name="bizGroupId" rules={[{ required: true, message: '请选择业务标签' }]}>
      <BizGroupSelector />
    </Form.Item>
    {isUpdate ? <Flex align="center" gap={token.marginXS}>
      <Flex flex={1}>
        <Form.Item label="URL" name="fullPath" style={{ width: '100%' }}>
          <Input
            disabled={isUpdate}
          />
        </Form.Item>
      </Flex>
      <Flex align="center">
        <Copy
          text={form.getFieldValue('fullPath')}
          styles={{
            position: 'relative',
            top: 8,
          }}
        >
          <CopyOutlined
            style={{
             fontSize: token.fontSizeXL,
            }}
          />
        </Copy>
      </Flex>
    </Flex> : <Form.Item
      label="Path"
      name="path"
      rules={[{ required: true, message: '请输入path' }, {
        pattern: /^[A-Za-z0-9_-]+$/,
        message: 'url只能由英文、数字、中下划线组成',
      }]}
    >
      <Input
        addonBefore="/mcp-server/"
        placeholder="path"
        addonAfter="/sse"
        disabled={isUpdate}
      />
    </Form.Item>}
    <Form.Item
      label={<Flex
        justify="space-between"
        align="center"
        flex={1}
      >
        <div>
          描述
        </div>
      </Flex>}
      name="description"
      rules={[{ required: true, message: '请输入描述' }]}
    >
      <Input.TextArea rows={3} />
    </Form.Item>
    <Form.Item
      label="Server图标"
      name="iconUrl"
    >
      <img src={defaultServerIcon} />
    </Form.Item>
    <Form.Item
      label="是否公开"
      name="accessLevel"
      valuePropName="checked"
      initialValue={false}
    >
      <Switch />
    </Form.Item>

    <Form.Item
      label={<Flex
        justify="space-between"
        align="center"
        flex={1}
      >
        <div>
          <span>
            选择你要关联的MCP Tool
          </span>
          <span
            style={{
              color: token.colorTextSecondary,
              fontSize: token.fontSizeSM,
              marginInlineStart: token.marginXS,
              fontWeight: 'normal',
            }}
          >
            建议一个MCP server关联的Tool为20个左右
          </span>
        </div>
      </Flex>}
      required
    >
      <McpToolTable
        selectable
      />
    </Form.Item>
  </Form>);
};


export default McpServerForm;
