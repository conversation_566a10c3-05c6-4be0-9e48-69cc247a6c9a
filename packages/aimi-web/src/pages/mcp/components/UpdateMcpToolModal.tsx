import { useRequest } from '@ali/mc-request';
import { getMcpToolMeta, GetMcpToolMetaParams } from '@ali/mc-services/AiMiMcpToolMeta';
import { Modal, Skeleton, theme } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import HttpApiSettingForm from './CreateMcpTool/HttpApiSettingForm';
import { McpToolMeta } from '@ali/mc-services/AiMiMcpDataContracts';
import { calcSchemaPath, calcSchemaPathAndRequired, flattenSchema, updateSchemaMetadata, updateSchemaMetadataByIdentifier, updateSchemaWithName } from './CreateMcpTool/utils';
import { isEmpty, omit, pick } from 'lodash-es';
import HsfApiSettingForm from './CreateMcpTool/HsfApiSettingForm';
import { InlineButton } from '@ali/mc-uikit';
import { GearOutlined } from '@ali/mc-icons';


const getUpdatedSchema = (oldJsonSchema?: string, newJsonSchema?: string) => {
  // 注册时的值
  const oldSchema = JSON.parse(oldJsonSchema || '{}');
  // 实时
  const newSchema = JSON.parse(newJsonSchema || '{}');
  const oldSchemaWithPath = calcSchemaPathAndRequired(oldSchema, false);
  const newSchemaWithPath = calcSchemaPathAndRequired(newSchema, true);
  if (!newJsonSchema) {
    return oldSchemaWithPath;
  }

  const flattenOldSchema = flattenSchema(oldSchemaWithPath);

  // isRequired和description以注册时的为准
  const updatedSchema = updateSchemaMetadata(flattenOldSchema, newSchemaWithPath, ['description', 'isRequired__']);

  // disableRequire字段以实时结果为准；用实时的disableRequire去更新原来的；
  const flattenNewSchema = flattenSchema(newSchemaWithPath);
  const updatedSchemaRequired = updateSchemaMetadata(flattenNewSchema, updatedSchema, ['disableRequire__']);
  return updatedSchemaRequired;
};

const getUpdatedHsfSchema = (oldSchema?: any, newSchema?: any) => {
  const oldSchemaWithPath = calcSchemaPathAndRequired(oldSchema, false);
  const newSchemaWithPath = calcSchemaPathAndRequired(newSchema, true);
  if (isEmpty(newSchema)) {
    return oldSchemaWithPath;
  }

  const oldFlattenSchema = flattenSchema(oldSchemaWithPath, 'identifier__');
  const newFlattenSchema = flattenSchema(newSchemaWithPath, 'identifier__');
  // name, isRequired, description以注册时的为准
  const updatedSchema = updateSchemaMetadataByIdentifier(oldFlattenSchema, newSchemaWithPath, ['name__', 'isRequired__', 'description']);
  const updatedSchemaRequired = updateSchemaMetadataByIdentifier(newFlattenSchema, updatedSchema, ['disableRequire__']);
  const updateSchemaName = updateSchemaWithName(updatedSchemaRequired);
  const updatePath = calcSchemaPath(updateSchemaName);
  return updatePath;
};

const UpdateMcpToolModal = ({ id, onRefresh }: {id?: number; onRefresh: () => void}) => {
  const { token } = theme.useToken();
  const [open, setOpen] = useState<boolean>(false);
  const [needRefresh, setNeedRefresh] = useState<boolean>(false);

  const handleOpen = useCallback(() => {
    setOpen(true);
  }, []);

  const handleClose = useCallback(() => {
    setOpen(false);
  }, []);

  const {
    runAsync: getToolDetail,
    data: detail,
    mutate: setDetail,
    loading,
  } = useRequest<McpToolMeta, [GetMcpToolMetaParams]>(getMcpToolMeta, {
    onSuccess: (res) => {
      const { newResponseJsonSchema, responseJsonSchema, paramJsonSchema, newParamJsonSchema } = (res || {}) as any;
      if (res?.type === 'HTTP') {
        const updatedResponseSchemaWithPath = getUpdatedSchema(responseJsonSchema, newResponseJsonSchema);
        const updatedParamSchemaWithPath = getUpdatedSchema(paramJsonSchema, newParamJsonSchema);
        const { parameters = [], requestBody = {} } = JSON.parse(res?.paramJson || '{}');
        const paramKeys = parameters?.map(i => i.name ?? '') ?? [];
        let paramSchema;
        if (paramKeys.length) {
        paramSchema = {
            ...updatedParamSchemaWithPath,
            properties: pick(updatedParamSchemaWithPath?.properties, paramKeys),
          };
        }

        let bodySchema: any;
        if (!isEmpty(requestBody)) {
          bodySchema = {
            ...updatedParamSchemaWithPath,
            properties: omit(updatedParamSchemaWithPath?.properties, paramKeys),
          };
        }
        // console.log(updatedParamSchema, 'updatedParamSchema==>', paramSchema, bodySchema, updatedResponseSchema);
        setDetail({
          ...res,
          api: {
            path: res?.action,
            httpMethod: res?.httpMethod,
            parameters: JSON.parse(res?.paramJson || '{}').parameters,
            description: res?.description,
            serverUrl: res?.domain,
          },
          initialParamJsonSchemaWithPath: updatedParamSchemaWithPath,
          ...(!isEmpty(paramSchema) && { paramSchemaWithPath: paramSchema }),
          ...(!isEmpty(bodySchema) && { bodySchemaWithPath: bodySchema }),
          ...(responseJsonSchema && { responseSchemaWithPath: updatedResponseSchemaWithPath }),
        } as any);
      }

      if (res?.type === 'HSF') {
        const { paramJson, newParamJson } = (res || {}) as any;
        let parsedParamJsonSchema = JSON.parse(paramJsonSchema ?? '{}');
        let newParsedParamJsonSchema = JSON.parse(newParamJsonSchema ?? '{}');
        if (parsedParamJsonSchema && paramJson) {
          const parsedParamJson = JSON.parse(paramJson);
          parsedParamJsonSchema = {
            ...parsedParamJsonSchema,
            parameterTypes: parsedParamJson?.parameterTypes ?? [],
          };
        }
        if (newParsedParamJsonSchema && newParamJson) {
          const parsedNewParamJson = JSON.parse(newParamJson);
          newParsedParamJsonSchema = {
            ...newParsedParamJsonSchema,
            parameterTypes: parsedNewParamJson?.parameterTypes ?? [],
          };
        }

        const oldResSchema = JSON.parse(responseJsonSchema || '{}');
        const newResSchema = JSON.parse(newResponseJsonSchema || '{}');

        const paramSchemaWithPath = getUpdatedHsfSchema(parsedParamJsonSchema, newParsedParamJsonSchema);
        const responseSchemaWithPath = getUpdatedHsfSchema(oldResSchema, newResSchema);

        setDetail({
          ...res,
          ...(paramJsonSchema && { paramSchemaWithPath }),
          ...(responseJsonSchema && { responseSchemaWithPath }),
        });
      }
    },
  });

  useEffect(() => {
    if (id && open) {
      getToolDetail({
        id,
      });
    }
  }, [id, getToolDetail, open]);

  return (<>
    <InlineButton
      type="text"
      onClick={handleOpen}
    >
      <GearOutlined />
    </InlineButton>
    <Modal
      open={open}
      onCancel={handleClose}
      onClose={handleClose}
      title="更新MCP Tool"
      footer={null}
      afterClose={() => {
        needRefresh && onRefresh();
      }}
      style={{
        minWidth: '80vw',
      }}
      styles={{
        body: {
          height: 'calc(100vh - 250px)',
          overflowY: 'auto',
          paddingBlock: token.padding,
          minHeight: 498,
        },
        footer: {
          marginTop: 0,
        },
        header: {
          marginBottom: 0,
        },
      }}
    >
      <Skeleton loading={loading} active>
        {
          detail?.type === 'HTTP' && <HttpApiSettingForm
            isUpdate
            onClose={handleClose}
            initialValues={detail as any}
            setNeedRefresh={setNeedRefresh}
          />
        }
        {
          detail?.type === 'HSF' && <HsfApiSettingForm
            isUpdate
            onClose={handleClose}
            initialValues={detail as any}
            setNeedRefresh={setNeedRefresh}
          />
        }
      </Skeleton>

    </Modal>

  </>);
};

export default UpdateMcpToolModal;
