{"name": "@continuedev/fetch", "version": "1.0.10", "description": "", "main": "dist/index.js", "types": "dist/index.d.ts", "type": "module", "scripts": {"test": "cross-env NODE_OPTIONS=--experimental-vm-modules jest", "build": "tsc"}, "author": "<PERSON> and <PERSON>", "license": "Apache-2.0", "dependencies": {"@continuedev/config-types": "^1.0.5", "follow-redirects": "^1.15.6", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.5", "node-fetch": "^3.3.2"}, "devDependencies": {"@types/follow-redirects": "^1.14.4", "@types/jest": "^29.5.14", "cross-env": "^7.0.3", "jest": "^29.7.0", "ts-jest": "^29.3.4", "ts-node": "^10.9.2"}}