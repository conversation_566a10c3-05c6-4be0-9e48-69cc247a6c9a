/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  AICrRecordVO,
  AiCrResponse,
  MethodTopVO,
  ModuleIncrCodeScanGateRecordRuleItemVO,
  ModuleIncrCodeScanMethodDiffVO,
  MtlCodeReviewCreateDTO,
  RemindRequest,
} from './DataContracts';
import { MethodOptions, getBaseUrl } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/dev');

export interface GetAiCrResponseParams {
  /**
   * projectId
   * @format int32
   */
  projectId: number;
  /**
   * mergeRequestId
   * @format int32
   */
  mergeRequestId: number;
}
/**
 * No description
 * @tags AICodeReview
 * @name GetAiCrResponse
 * @summary ai代码评审
 * @request GET:/api/v1/aiCr
 */
export async function getAiCrResponse(query: GetAiCrResponseParams, options?: MethodOptions): Promise<AiCrResponse> {
  return request(`${baseUrl}/api/v1/aiCr`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface AcceptCodeReviewParams {
  /**
   * mergeRequestId
   * @format int64
   */
  mergeRequestId: number;
}
/**
 * No description
 * @tags AICodeReview
 * @name AcceptCodeReview
 * @summary 通过代码评审
 * @request POST:/api/v1/aiCr/accept
 */
export async function acceptCodeReview(query: AcceptCodeReviewParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/aiCr/accept`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

export interface FindReviewersParams {
  /**
   * moduleId
   * @format int32
   */
  moduleId: number;
}
/**
 * No description
 * @tags AICodeReview
 * @name FindReviewers
 * @summary 根据模块查看常用评审人
 * @request GET:/api/v1/aiCr/findReviewers
 */
export async function findReviewers(query: FindReviewersParams, options?: MethodOptions): Promise<string[]> {
  return request(`${baseUrl}/api/v1/aiCr/findReviewers`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetMatchRulesParams {
  /**
   * scanResultId
   * @format int64
   */
  scanResultId: number;
  /** type */
  type?: string;
  /**
   * methodChartId
   * @format int64
   */
  methodChartId?: number;
}
/**
 * No description
 * @tags AICodeReview
 * @name GetMatchRules
 * @summary 查看匹配规则
 * @request GET:/api/v1/aiCr/getMatchRules
 */
export async function getMatchRules(
  query: GetMatchRulesParams,
  options?: MethodOptions,
): Promise<ModuleIncrCodeScanGateRecordRuleItemVO[]> {
  return request(`${baseUrl}/api/v1/aiCr/getMatchRules`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetMethodDiffParams {
  /**
   * aiCrRecordId
   * @format int64
   */
  aiCrRecordId: number;
  /**
   * scanResultId
   * @format int64
   */
  scanResultId: number;
  /**
   * methodChartId
   * @format int64
   */
  methodChartId: number;
}
/**
 * No description
 * @tags AICodeReview
 * @name GetMethodDiff
 * @summary 增量代码扫描方法diff
 * @request GET:/api/v1/aiCr/getMethodDiff
 */
export async function getMethodDiff(
  query: GetMethodDiffParams,
  options?: MethodOptions,
): Promise<ModuleIncrCodeScanMethodDiffVO> {
  return request(`${baseUrl}/api/v1/aiCr/getMethodDiff`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetMethodTopParams {
  /**
   * aiCrRecordId
   * @format int64
   */
  aiCrRecordId: number;
}
/**
 * No description
 * @tags AICodeReview
 * @name GetMethodTop
 * @summary 查看方法拓扑
 * @request GET:/api/v1/aiCr/getMethodTop
 */
export async function getMethodTop(query: GetMethodTopParams, options?: MethodOptions): Promise<MethodTopVO> {
  return request(`${baseUrl}/api/v1/aiCr/getMethodTop`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

export interface GetRecordParams {
  /**
   * aiCrRecordId
   * @format int64
   */
  aiCrRecordId: number;
}
/**
 * No description
 * @tags AICodeReview
 * @name GetRecord
 * @summary 根据id查看cr评审记录
 * @request GET:/api/v1/aiCr/getRecord
 */
export async function getRecord(query: GetRecordParams, options?: MethodOptions): Promise<AICrRecordVO> {
  return request(`${baseUrl}/api/v1/aiCr/getRecord`, {
    method: 'GET',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags AICodeReview
 * @name InitiatedList
 * @summary 查看我发起的cr评审记录
 * @request GET:/api/v1/aiCr/initiated/list
 */
export async function initiatedList(options?: MethodOptions): Promise<AICrRecordVO[]> {
  return request(`${baseUrl}/api/v1/aiCr/initiated/list`, {
    method: 'GET',
    ...options,
  });
}

export interface MergeCodeReviewParams {
  /**
   * aiCrRecordId
   * @format int64
   */
  aiCrRecordId: number;
  /** codeMergeType */
  codeMergeType: 'FAST_FORWARD_ONLY' | 'NO_FAST_FORWARD' | 'REBASE' | 'REBASE_WITH_MESSAGE' | 'SQUASH';
  /** mergeCommitMessage */
  mergeCommitMessage?: string;
  /** shouldRemoveSourceBranch */
  shouldRemoveSourceBranch?: boolean;
}
/**
 * No description
 * @tags AICodeReview
 * @name MergeCodeReview
 * @summary 合并代码评审
 * @request POST:/api/v1/aiCr/merge
 */
export async function mergeCodeReview(query: MergeCodeReviewParams, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/aiCr/merge`, {
    method: 'POST',
    params: query,
    ...options,
  });
}

/**
 * No description
 * @tags AICodeReview
 * @name CreateCodeReview
 * @summary aimi依赖摩天轮模块创建代码评审
 * @request POST:/api/v1/aiCr/mtl/create
 */
export async function createCodeReview(data: MtlCodeReviewCreateDTO, options?: MethodOptions): Promise<number> {
  return request(`${baseUrl}/api/v1/aiCr/mtl/create`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags AICodeReview
 * @name Remind
 * @summary 发起催办
 * @request POST:/api/v1/aiCr/remind
 */
export async function remind(data: RemindRequest, options?: MethodOptions): Promise<boolean> {
  return request(`${baseUrl}/api/v1/aiCr/remind`, {
    method: 'POST',
    body: data as any,
    ...options,
  });
}

/**
 * No description
 * @tags AICodeReview
 * @name ReviewList
 * @summary 查看我评审的cr评审记录
 * @request GET:/api/v1/aiCr/review/list
 */
export async function reviewList(options?: MethodOptions): Promise<AICrRecordVO[]> {
  return request(`${baseUrl}/api/v1/aiCr/review/list`, {
    method: 'GET',
    ...options,
  });
}
