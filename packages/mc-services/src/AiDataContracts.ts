/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

type UtilRequiredKeys<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;

export interface AgentInstanceBO {
  type?: 'RAG';
  name?: string;
  identifier?: string;
  description?: string;
  status?: 'ONLINE' | 'OFFLINE';
  groupIdentifier?: string;
  promptTemplate?: string;
  promptExecutionSettings?: PromptExecutionSettingsDTO;
  knowledgeBaseBindVariableKey?: string;
  knowledgeBases?: KnowledgeConfigBO[];
  mcpServerMetas?: AgentInstanceMcpServer[];
}

export interface AgentInstanceMcpServer {
  /** @format int64 */
  id?: number;
  name?: string;
  description?: string;
  sseUrl?: string;
}

export interface DataSearchConfig {
  retrievalMethods?: ('EMBEDDING' | 'KEYWORD')[];
  embeddingConfig?: EmbeddingConfig;
  keywordConfig?: KeywordConfig;
  rerankConfig?: RerankConfig;
}

export interface EmbeddingConfig {
  retrievalType?: 'ORIGIN_CHUNK' | 'ORIGIN_FILE';
  /** @format int32 */
  retrievalCounts?: number;
  /** @format double */
  threshold?: number;
  tags?: string[];
}

export interface KeywordConfig {
  retrievalType?: 'ORIGIN_CHUNK' | 'ORIGIN_FILE';
  /** @format int32 */
  retrievalCounts?: number;
  /** @format double */
  threshold?: number;
  tags?: string[];
}

export interface KnowledgeConfigBO {
  identifier?: string;
  name?: string;
  description?: string;
  searchConfig?: DataSearchConfig;
}

export interface PromptExecutionSettingsDTO {
  service_id?: string;
  model_id?: string;
  /** @format double */
  temperature?: number;
  /** @format double */
  top_p?: number;
  /** @format int32 */
  top_k?: number;
  /** @format int64 */
  seed?: number;
  /** @format double */
  presence_penalty?: number;
  /** @format double */
  frequency_penalty?: number;
  /** @format int32 */
  max_tokens?: number;
  stop_sequences?: string[];
  token_selection_biases?: Record<string, number>;
  response_format?: ResponseFormat;
}

export interface RerankConfig {
  model?: string;
}

export interface ResponseFormat {
  type?: string;
}

export interface TypeVoid {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: object;
  ip?: string;
}

export interface ChatResponseFunctionCall {
  name?: string;
  arguments?: string;
}

export type AllLocalPluginConfig = SkillConfig;

export type AssistantAgentConfig = BaseAgentConfig;

export interface BaseAgentConfig {
  name?: string;
  identifier?: string;
  kernel?: KernelConfig;
  /** @format int32 */
  maxConsecutiveAutoReply?: number;
  description?: string;
  humanInputMode?: 'ALWAYS' | 'TERMINATE' | 'NEVER';
  systemMessage?: string;
  defaultSummaryPromptConfig?: PromptConfig | DbPromptConfig | TextPromptConfig;
  chatFunction?: PromptConfig | DbPromptConfig | TextPromptConfig;
  mcpServerConfigs?: (SseMcpConfig | StdioMcpConfig)[];
  executeSettings?: PromptExecutionSettingsDTO;
  parserConfig?: DynamicYamlParserConfig | DynamicYamlParserJsonConfig | JsonObjectParserConfig | YamlParserConfig;
  type: string;
}

export interface BaseMcpServerConfig {
  name?: string;
  description?: string;
  type: string;
}

export type BuildAgentConfig = BaseAgentConfig & {
  extra?: Record<string, object>;
};

export type CodeRewriteAgentConfig = BaseAgentConfig & {
  improveResultFunction?: PromptConfig | DbPromptConfig | TextPromptConfig;
  reflectionFunction?: PromptConfig | DbPromptConfig | TextPromptConfig;
};

export type DbPromptConfig = UtilRequiredKeys<PromptConfig, 'type'> & {
  bizIdentifier?: string;
  type: string;
};

export type DynamicAgentConfig = BaseAgentConfig & {
  className?: string;
};

export type DynamicYamlParserConfig = ParserConfig & {
  classDefineScript?: string;
  formatInstructionsTemplate?: string;
};

export type DynamicYamlParserJsonConfig = ParserConfig & {
  fieldDefinitions?: FieldDefinition[];
};

export interface FieldDefinition {
  type?: string;
  name?: string;
  description?: string;
}

export interface JSONObject {
  empty?: boolean;
  innerMap?: Record<string, object>;
  [key: string]: any;
}

export type JsonObjectParserConfig = ParserConfig;

export interface KernelConfig {
  model?:
    | 'O1_PREVIEW_0912'
    | 'O1_PREVIEW_0912_GLOBAL'
    | 'O1_MINI_0912'
    | 'O1_MINI_0912_GLOBAL'
    | 'GPT4O_0806_GLOBAL'
    | 'GPT4O_0806'
    | 'GPT4O_MINI_0718'
    | 'GPT4O_MINI_0718_GLOBAL'
    | 'GPT4_0409'
    | 'GPT4O_0513_GLOBAL'
    | 'GPT4O_0513'
    | 'GPT4_TURBO_128K'
    | 'GPT4_8K'
    | 'GPT4_32K'
    | 'GPT35_16K_1106'
    | 'GPT35_TURBO'
    | 'GPT35_16K'
    | 'QWEN2'
    | 'QWEN_VL_MAX'
    | 'IDEALAB_QWEN_PLUS'
    | 'LLAMA3'
    | 'QWEN2_7B_INSTRUCT'
    | 'QWEN2_72B_INSTRUCT'
    | 'QWEN2_5_72B_INSTRUCT'
    | 'QWEN2_5_32B_INSTRUCT'
    | 'QWEN2_5_14B_INSTRUCT'
    | 'QWEN2_5_7B_INSTRUCT'
    | 'QWEN2_5_3B_INSTRUCT'
    | 'QWEN2_5_1_5B_INSTRUCT'
    | 'QWEN2_5_0_5B_INSTRUCT'
    | 'QWEN2_5_CODER_7B_INSTRUCT'
    | 'GEMINI15_PRO'
    | 'GEMINI_PRO'
    | 'GEMINI_PRO_VISION'
    | 'GEMINI15_PRO_FLASH'
    | 'GEMINI25_PRO'
    | 'CLAUDE3_HAIKU'
    | 'CLAUDE3_SONNET'
    | 'CALUDE3_OPUS'
    | 'CLAUDE35_SONNET'
    | 'CLAUDE35_SONNET2'
    | 'CLAUDE37_SONNET'
    | 'DEEPSEEK_R1_671B'
    | 'DEEPSEEK_R1_DISTILL_QWEN_32B'
    | 'DEEPSEEK_R1_DISTILL_QWEN_14B'
    | 'DEEPSEEK_R1'
    | 'DEEPSEEK_V3_671B'
    | 'WHALE_QWEN2_72B'
    | 'WHALE_QWEN2_7B'
    | 'WHALE_DOCSTRING_V0'
    | 'WHALE_RULE_MATCH'
    | 'MTLCoder'
    | 'AIMI_CHAT_72B'
    | 'QWEN_PLUS'
    | 'QWEN_MAX'
    | 'QWEN_LONG'
    | 'QWQ_PLUS'
    | 'QWQ_32B'
    | 'BAILIAN_DEEPSEEK_R1'
    | 'QWEN3_235B_A22B'
    | 'QWEN3_32B'
    | 'QWEN3_30B_A3B'
    | 'QWEN3_14B';
  skills?: (SkillConfig | AllLocalPluginConfig | LocalPluginConfig | OpenApiPluginConfig)[];
}

export interface KnowledgeBizInfo {
  bizName?: string;
  storageType?: 'ES_VECTOR' | 'ALIYUN_ES_VECTOR';
  storageSearchConfig?: StorageSearchConfig;
  extInfo?: Record<string, string>;
}

export type LocalPluginConfig = SkillConfig & {
  pluginNames?: string[];
};

export type OpenApiPluginConfig = SkillConfig & {
  pluginName?: string;
  schemaContent?: string;
  schemaUrl?: string;
  serverUrl?: string;
  headersMap?: Record<string, string>;
};

export type ParserAgentConfig = BaseAgentConfig;

export interface ParserConfig {
  type: string;
}

export interface PromptConfig {
  templateFormat?: string;
  type: string;
}

export type RagAgentConfig = BaseAgentConfig & {
  knowledgeBizInfo?: KnowledgeBizInfo;
  /** @format int32 */
  recommendCount?: number;
  /** @format int32 */
  tokenLimit?: number;
  /** @format double */
  similarityThreshold?: number;
  questionPromptTemplate?: string;
};

export type RagAgentV2Config = BaseAgentConfig & {
  knowledgeBaseBindVariableKey?: string;
  knowledgeBases?: KnowledgeConfigBO[];
};

export type SequentialFlowAgentConfig = BaseAgentConfig & {
  agentChainConfigs?: BaseAgentConfig[];
};

export type SimpleReflectionAgentConfig = BaseAgentConfig & {
  reflectSystemMessage?: string;
  reGenerateTip?: string;
};

export interface SkillConfig {
  type: string;
}

export type SseMcpConfig = BaseMcpServerConfig & {
  baseUrl?: string;
  endpoint?: string;
};

export type StdioMcpConfig = BaseMcpServerConfig & {
  command?: string;
  args?: string[];
};

export interface StorageSearchConfig {
  modelName?: string;
}

export type TextPromptConfig = UtilRequiredKeys<PromptConfig, 'type'> & {
  promptText?: string;
  inputHistory?: string[][];
  executionSettings?: {
    empty?: boolean;
    innerMap?: Record<string, object>;
    [key: string]: any;
  };
  type: string;
};

export type YamlParserConfig = ParserConfig & {
  formatInstructionsTemplate?: string;
  formatNotNeedProperties?: string[];
};

export interface IncrCodeScanRequest {
  /** @format int64 */
  messageId?: number;
  /** @format int32 */
  projectId?: number;
  repo?: string;
  from?: string;
  to?: string;
  codeDir?: string;
  /** @format int64 */
  aimiCrRecordId?: number;
  /** @format int64 */
  appId?: number;
  appIdList?: number[];
  appVersion?: string;
  lastAppVersion?: string;
  /** @format int64 */
  alterSheetId?: number;
  /** @format int64 */
  pipelineInstanceId?: number;
  /** @format int64 */
  gateRecordId?: number;
  module?: string;
  /** @format int64 */
  moduleId?: number;
  moduleVersion?: string;
  moduleGroupId?: string;
  moduleArtifactId?: string;
  moduleType?: string;
  moduleDepKey?: string;
  /** @format int64 */
  spaceId?: number;
  /** @format int64 */
  moduleTeamSpaceId?: number;
  /** @format int64 */
  moduleBizSpaceId?: number;
  retry?: boolean;
  extensionField?: string;
  /** @format int64 */
  moduleRecordId?: number;
  incrScan?: boolean;
}

export interface CodeDiffMethodSet {
  /** @uniqueItems true */
  added?: DiffMethodMetadata[];
  /** @uniqueItems true */
  changed?: DiffMethodMetadata[];
  /** @uniqueItems true */
  deleted?: DiffMethodMetadata[];
  /** @uniqueItems true */
  related?: DiffMethodMetadata[];
  relations?: Relation[];
  /** @uniqueItems true */
  dependByModules?: DiffMethodModuleMetaData[];
}

export interface CodeSuggestionItem {
  /** @format int32 */
  noteId?: number;
  itemId?: string;
  status?: string;
  rejectReason?: string;
  modifier?: string;
  file?: string;
  ruleId?: string;
  rule?: string;
  language?: string;
  suggestion?: string;
  line?: string;
  /** @format int32 */
  lineNo?: number;
  process?: string;
  ruleTypes?: string[];
  ruleLevel?: string;
  /** @format int64 */
  ruleAppId?: number;
  /** @format int64 */
  ruleModuleId?: number;
  /** @format int64 */
  ruleSpaceId?: number;
  ruleStatus?: string;
  ruleEngine?: string;
}

export interface DiffMethodMetadata {
  identifier?: string;
  type?: string;
  /** @format int64 */
  appId?: number;
  appVersion?: string;
  repo?: string;
  ref?: string;
  filePath?: string;
  className?: string;
  methodName?: string;
  methodParams?: string;
  /** @format int32 */
  startRow?: number;
  /** @format int32 */
  endRow?: number;
  methodDocstring?: string;
  /** @format int64 */
  moduleId?: number;
  moduleDepGraphKey?: string;
  moduleVersion?: string;
  matchedModuleDepGraphKey?: string;
  matchedMethodSymbolIdentifier?: string;
  /** @format int64 */
  docstringCost?: number;
  /** @format int64 */
  symbolCost?: number;
}

export interface DiffMethodModuleMetaData {
  identifier?: string;
  /** @format int64 */
  moduleId?: number;
  methods?: DiffMethodMetadata[];
}

export interface IncrCodeScanResult {
  /** @format int64 */
  id?: number;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  repo?: string;
  from?: string;
  to?: string;
  request?: IncrCodeScanRequest;
  suggestions?: CodeSuggestionItem[];
  codeDiffMethodSet?: CodeDiffMethodSet;
  metric?: Metric;
}

export interface Metric {
  /** @format int64 */
  ruleMatchCost?: number;
  /** @format int64 */
  diffMethodSetCost?: number;
  /** @format int64 */
  diffMethodSetRelationCost?: number;
  /** @format int64 */
  diffDiagramCost?: number;
  /** @format int64 */
  totalCost?: number;
  /** @format int32 */
  addedMethodSize?: number;
  /** @format int32 */
  changedMethodSize?: number;
  /** @format int32 */
  deletedMethodSize?: number;
  /** @format int32 */
  totalMethodSize?: number;
  /** @format int32 */
  unmatchedMethodSize?: number;
  ip?: string;
}

export interface Relation {
  from?: string;
  to?: string;
  type?: string;
}

export interface AgentConfigAndChatDTO {
  baseAgentConfig?:
    | BaseAgentConfig
    | AssistantAgentConfig
    | BuildAgentConfig
    | CodeRewriteAgentConfig
    | DynamicAgentConfig
    | ParserAgentConfig
    | RagAgentConfig
    | RagAgentV2Config
    | SequentialFlowAgentConfig
    | SimpleReflectionAgentConfig;
  messageContents?: MessageDTO[];
}

export interface MessageDTO {
  authorRole?: 'system' | 'assistant' | 'user' | 'function' | 'tool';
  content?: string;
}

export interface MessageSendRequest {
  topic?: string;
  key?: string;
  tag?: string;
  body?: string;
  userProperties?: Record<string, string>;
}

export interface MessageSendResult {
  topic?: string;
  messageId?: string;
}

export interface CodeMethod {
  className?: string;
  methodName?: string;
  methodComment?: string;
  methodParams?: string;
  methodParamList?: CodeMethodParam[];
  methodSourceCode?: string;
  module?: string;
  repo?: string;
  ref?: string;
  filePath?: string;
  language?: string;
  /** @format int32 */
  startRow?: number;
  /** @format int32 */
  endRow?: number;
}

export interface CodeMethodParam {
  type?: string;
  identifier?: string;
}

export interface Tuple3SetCodeMethodSetCodeMethodSetCodeMethod {
  /** @uniqueItems true */
  t1?: CodeMethod[];
  /** @uniqueItems true */
  t2?: CodeMethod[];
  /** @uniqueItems true */
  t3?: CodeMethod[];
}

export interface CodeClass {
  packageName?: string;
  className?: string;
  categoryName?: string;
  superClassName?: string;
  /** @uniqueItems true */
  interfaceNames?: string[];
  classSourceCode?: string;
  classComment?: string;
  module?: string;
  repo?: string;
  ref?: string;
  filePath?: string;
  language?: string;
  /** @format int32 */
  startRow?: number;
  /** @format int32 */
  endRow?: number;
}

export interface CodeMethodContext {
  dependByMethods?: CodeMethod[];
  dependOnMethods?: CodeMethod[];
  codeClass?: CodeClass;
}

export interface AgentTaskRequest {
  scene?: string;
  /** @format int64 */
  sceneEntityId?: number;
  params?: object;
  bizEntityDiscernConfig?: BizEntityDiscernConfig;
}

export interface BizEntityDiscernConfig {
  bizIdentifier?: string;
  bizEntityList?: BizEntityItemInfo[];
  example?: string;
}

export interface BizEntityItemInfo {
  identifier?: string;
  desc?: string;
  expressionList?: string[];
  extra?: string;
}

export interface AgentEventDetailObject {
  role?: string;
  responseType?: 'TEXT' | 'FUNCTION_TEXT' | 'TOOL_FUNCTION' | 'TOOL_CODE';
  content?: object[];
}

export interface AgentInfo {
  identifier?: string;
  name?: string;
  desc?: string;
  llmInfo?: string;
  tools?: string;
  metadata?: Record<string, object>;
}

export interface AgentInteractionEventBO {
  /** @format int64 */
  id?: number;
  /** @format int64 */
  interactionId?: number;
  fromAgent?: string;
  currentAgent?: string;
  type?: 'CHAT';
  metadata?: Record<string, object>;
  input?: string;
  response?: AgentEventDetailObject;
  status?: 'SUCCESS' | 'FAIL' | 'EXCEPTION' | 'UNKNOWN';
  /** @format date-time */
  startTime?: string;
  /** @format date-time */
  endTime?: string;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
}

export interface AgentsInteractionsBO {
  /** @format int64 */
  id?: number;
  /** @format int64 */
  sceneEntityId?: number;
  sceneEntityType?: string;
  status?: 'NOT_STARTED' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'CANCELED';
  thoughtMode?:
    | 'SIMPLE_AGENT'
    | 'SINGLE_ROUND'
    | 'MULTI_ROUND'
    | 'TWO_AGENT'
    | 'ANALYZE_SUMMARIZE_AGENT'
    | 'BUILD_SUMMARIZE_AGENT'
    | 'GROUP_AGENT_AUTO'
    | 'MULTI_CODE_REWRITE_RANK';
  thoughtModeDetail?: string;
  goal?: string;
  result?: string;
  agents?: AgentInfo[];
  interactionEventList?: AgentInteractionEventBO[];
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
}

export interface NodeInputConfigBaseAgentConfig {
  type?: string;
  name?: string;
  source?:
    | BaseAgentConfig
    | AssistantAgentConfig
    | BuildAgentConfig
    | CodeRewriteAgentConfig
    | DynamicAgentConfig
    | ParserAgentConfig
    | RagAgentConfig
    | RagAgentV2Config
    | SequentialFlowAgentConfig
    | SimpleReflectionAgentConfig;
}

export type AIServiceSelector = object;

export interface BaseOutputParserObjectString {
  parserType?: string;
  formatInstructions?: string;
}

export interface ChatMessageContentObject {
  innerContent?: object;
  metadata?: FunctionResultMetadata;
  authorRole?: 'system' | 'assistant' | 'user' | 'function' | 'tool';
  content?: string;
  encoding?: string;
  name?: string;
  contentType?: 'TEXT' | 'IMAGE_URL';
  extra?: Record<string, object>;
  agents?: AgentInfo[];
  /** @format date-time */
  gmtCreate?: string;
}

export interface ContextVariableObject {
  type?: ContextVariableTypeObject;
  value?: object;
  empty?: boolean;
}

export type ContextVariableTypeConverterObject = object;

export interface ContextVariableTypeObject {
  converter?: ContextVariableTypeConverterObject;
}

export interface ConversableAgent {
  kernel?: Kernel;
  identifier?: string;
  name?: string;
  systemMessage?: string;
  description?: string;
  prompt?: string;
  sessionId?: string;
  defaultSummaryPrompt?: string;
  terminateMsg?: string;
  chatFunction?: KernelFunctionObject;
  promptExecutionSettings?: PromptExecutionSettings;
  isTerminationMsg?: object;
  /** @format int32 */
  maxConsecutiveAutoReply?: number;
  humanInputMode?: 'ALWAYS' | 'TERMINATE' | 'NEVER';
  defaultAutoReply?: string;
  consecutiveAutoReplyCounter?: Record<string, number>;
  lastGenerateResults?: Record<string, GenerateReplyResult>;
  oaiSystemMessage?: ChatMessageContentObject[];
  oaiMessages?: Record<string, ChatMessageContentObject[]>;
  replyFuncList?: object[];
  messagesConsumer?: object;
  outputParser?: BaseOutputParserObjectString;
  supportPlatforms?: ('IDE' | 'WEB' | 'API')[];
  agentInfo?: AgentInfo;
}

export interface FunctionResultMetadata {
  metadata?: {
    empty?: boolean;
    [key: string]: any;
  };
  id?: string;
  /** @format date-time */
  createdAt?: string;
  usage?: object;
}

export interface GenerateReplyResult {
  replies?: ChatMessageContentObject[];
  contextVariables?: Record<string, object>;
  agentVariables?: Record<string, Record<string, object>>;
}

export interface InputVariable {
  name?: string;
  type?: string;
  description?: string;
  default?: string;
  is_required?: boolean;
  enum?: object[];
  defaultValue?: string;
  enumValues?: object[];
  required?: boolean;
}

export type JsonObjectResponseFormat = ResponseFormat;

export interface JsonResponseSchema {
  name?: string;
  schema?: string;
  strict?: boolean;
}

export type JsonSchemaResponseFormat = ResponseFormat & {
  json_schema?: JsonResponseSchema;
};

export interface Kernel {
  serviceSelector?: AIServiceSelector;
  plugins?: KernelPlugin[];
  globalKernelHooks?: KernelHooks;
  process?: boolean;
  functions?: KernelFunctionObject[];
}

export interface KernelFunctionMetadataObject {
  name?: string;
  pluginName?: string;
  description?: string;
  parameters?: InputVariable[];
  outputVariableType?: OutputVariableObject;
}

export interface KernelFunctionObject {
  metadata?: KernelFunctionMetadataObject;
  executionSettings?: Record<string, PromptExecutionSettings>;
  name?: string;
  pluginName?: string;
  defaultExecutionSettings?: PromptExecutionSettings;
  description?: string;
}

export interface KernelHooks {
  empty?: boolean;
}

export interface KernelPlugin {
  name?: string;
  description?: string;
  functions?: Record<string, KernelFunctionObject>;
  metadata?: Record<string, object>;
}

export interface OutputVariableObject {
  description?: string;
}

export interface PromptExecutionSettings {
  service_id?: string;
  model_id?: string;
  /** @format double */
  temperature?: number;
  /** @format double */
  top_p?: number;
  /** @format double */
  presence_penalty?: number;
  /** @format double */
  frequency_penalty?: number;
  /** @format int32 */
  max_tokens?: number;
  /** @format int32 */
  results_per_prompt?: number;
  /** @format int32 */
  best_of?: number;
  user?: string;
  stop_sequences?: string[];
  token_selection_biases?: Record<string, number>;
  response_format?: JsonObjectResponseFormat | JsonSchemaResponseFormat | TextResponseFormat;
  cacheTools?: boolean;
  cacheSystemPrompt?: boolean;
}

export type TextResponseFormat = ResponseFormat;

export interface PromptTemplateDTO {
  bizIdentifier?: string;
  promptTemplate?: string;
  inputVariables?: string[];
  inputHistory?: string[][];
  matchCondition?: string;
  executionSettings?: string;
  format?: 'SEMANTIC_KERNEL' | 'HANDLEBARS' | 'TEXT';
}

export interface AskLLMDTO {
  prompt?: string;
  history?: string[][];
  /** @format double */
  temperature?: number;
  /** @format int32 */
  maxTokens?: number;
  /** @format int32 */
  max_length?: number;
  model_name?: string;
  /** @format double */
  top_p?: number;
  /** @format double */
  top_k?: number;
  params?: Record<string, object>;
}

export interface LLMResponseVO {
  response?: string;
  history?: string[][];
  /** @format int32 */
  status?: number;
  /** @format double */
  time?: number;
  model?: string;
  endPoint?: string;
  /** @format double */
  topP?: number;
  /** @format double */
  temperature?: number;
  /** @format int32 */
  maxTokenLength?: number;
  extra?: Record<string, object>;
}

export interface TypeLLMResponseVO {
  success?: boolean;
  data?: LLMResponseVO;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalPages?: number;
  /** @format int64 */
  totalCount?: number;
  nullData?: boolean;
}

export interface ChatRequestDTO {
  messages?: OpenAIChatMessageContentObject[];
  service_id?: string;
  model_id?: string;
  /** @format double */
  temperature?: number;
  /** @format double */
  top_p?: number;
  /** @format int32 */
  top_k?: number;
  /** @format int64 */
  seed?: number;
  /** @format double */
  presence_penalty?: number;
  /** @format double */
  frequency_penalty?: number;
  /** @format int32 */
  max_tokens?: number;
  stop_sequences?: string[];
  token_selection_biases?: Record<string, number>;
  response_format?: ResponseFormat;
}

export interface ChatResponseToolCall {
  id?: string;
  type?: string;
  function?: ChatResponseFunctionCall;
}

export interface OpenAIChatMessageContentObject {
  innerContent?: object;
  metadata?: FunctionResultMetadata;
  authorRole?: 'system' | 'assistant' | 'user' | 'function' | 'tool';
  content?: string;
  encoding?: string;
  name?: string;
  contentType?: 'TEXT' | 'IMAGE_URL';
  extra?: Record<string, object>;
  agents?: AgentInfo[];
  /** @format date-time */
  gmtCreate?: string;
  tool_call_id?: string;
  tool_calls?: ChatResponseToolCall[];
}

export interface TypeString {
  success?: boolean;
  data?: string;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalPages?: number;
  /** @format int64 */
  totalCount?: number;
  nullData?: boolean;
}

export interface AiStudioAzureChatRequest {
  prompt?: string;
  history?: string[][];
  /** @format double */
  temperature?: number;
  /** @format int32 */
  maxTokens?: number;
  messages?: (
    | ChatRequestMessage
    | ChatRequestAssistantMessage
    | ChatRequestFunctionMessage
    | ChatRequestSystemMessage
    | ChatRequestToolMessage
    | ChatRequestUserMessage
  )[];
  stream?: boolean;
  stop?: string[];
  /** @format double */
  presencePenalty?: number;
  /** @format double */
  frequencyPenalty?: number;
  logitBias?: Record<string, number>;
  functions?: FunctionDefinition[];
  functionCall?: object;
  tools?: ToolDefinition[];
  toolChoice?: object;
  businessCode?: string;
  scenesCode?: string;
  lastMessageIds?: number[];
  needAppend?: boolean;
  contentType?: string;
  curiosity?: string;
  suffix?: string;
  modelVersion?: string;
  extendParams?: Record<string, object>;
  /** @format int32 */
  max_length?: number;
  model?: string;
  /** @format double */
  top_p?: number;
  /** @format double */
  top_k?: number;
  params?: Record<string, object>;
}

export type ChatRequestAssistantMessage = UtilRequiredKeys<ChatRequestMessage, 'role'> & {
  content?: object;
  name?: string;
  tool_calls?: ChatResponseToolCall[];
  function_call?: ChatResponseFunctionCall;
  role: string;
};

export type ChatRequestFunctionMessage = UtilRequiredKeys<ChatRequestMessage, 'role'> & {
  name?: string;
  content?: object;
  role: string;
};

export interface ChatRequestMessage {
  cache_control?: {
    empty?: boolean;
    innerMap?: Record<string, object>;
    [key: string]: any;
  };
  role: string;
}

export type ChatRequestSystemMessage = UtilRequiredKeys<ChatRequestMessage, 'role'> & {
  content?: object;
  name?: string;
  role: string;
};

export type ChatRequestToolMessage = UtilRequiredKeys<ChatRequestMessage, 'role'> & {
  content?: string;
  tool_call_id?: string;
  role: string;
};

export type ChatRequestUserMessage = UtilRequiredKeys<ChatRequestMessage, 'role'> & {
  content?: object;
  name?: string;
  role: string;
};

export interface FunctionDefinition {
  name?: string;
  description?: string;
  parameters?: FunctionParameters;
  cache_control?: {
    empty?: boolean;
    innerMap?: Record<string, object>;
    [key: string]: any;
  };
}

export interface FunctionParameters {
  type?: string;
  properties?: Record<string, JSONObject>;
  required?: string[];
}

export interface ToolDefinition {
  type?: string;
  function?: FunctionDefinition;
}

export interface AiStudioGPTResult {
  /** @format int64 */
  messageId?: number;
  content?: string;
  /** @format int64 */
  promptTokens?: number;
  /** @format int64 */
  totalTokens?: number;
  /** @format int64 */
  completionTokens?: number;
}

export interface TypeAiStudioGPTResult {
  success?: boolean;
  data?: AiStudioGPTResult;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalPages?: number;
  /** @format int64 */
  totalCount?: number;
  nullData?: boolean;
}

export interface KnowledgeQueryDTO {
  knowledgeBizInfo?: KnowledgeBizInfo;
  query?: string;
  metadata?: Record<string, object>;
  /** @format int32 */
  recommendCount?: number;
  /** @format double */
  similarityThreshold?: number;
}

export interface DocumentItem {
  documentId?: string;
  text?: string;
  vector?: number[];
  vectors?: Record<string, number[]>;
  content?: Record<string, object>;
  metadata?: Record<string, object>;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  creator?: string;
  modifier?: string;
}

export interface TypeBoolean {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: boolean;
  ip?: string;
}

export interface EvalContentDTO {
  /** @format int64 */
  id?: number;
  /** @format int64 */
  evalJobId?: number;
  /** @format int64 */
  evalJobInstanceId?: number;
  evalInput?: Record<string, string>;
  evalOutput?: Record<string, string>;
  evalAnswer?: string;
  status?: 'SUCCESS' | 'UNCERTAIN' | 'NORMALIZED_ERROR';
  evalTool?: EvalTool;
  modifiedAnswer?: string;
  extra?: Record<string, string>;
}

export interface EvalTool {
  type?: 'MTL_PIPELINE';
  desc?: string;
}

export interface ResultBoolean {
  success?: boolean;
  data?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
}

export interface ResultVoid {
  success?: boolean;
  data?: object;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
}

export interface DataEvalJobDTO {
  evalJobName?: string;
  evalJobDesc?: string;
  triggerCron?: string;
  triggerExtra?: Record<string, string>;
  evalTool?: EvalTool;
  toolRunTemplate?: Record<string, object>;
  jobExtra?: Record<string, string>;
  dataSourceTemplate?: Record<string, string>;
  isDeleted?: boolean;
}

export interface ResultLong {
  success?: boolean;
  /** @format int64 */
  data?: number;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
}

export interface EmbeddingRequest {
  modelName?: string;
  /** @format int32 */
  maxTokenLength?: number;
  data?: string;
}

export interface TypeEmbeddingResult {
  success?: boolean;
  data?: EmbeddingResult;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalPages?: number;
  /** @format int64 */
  totalCount?: number;
  nullData?: boolean;
}

export interface EmbeddingResult {
  modelName?: string;
  /** @format int32 */
  maxTokenLength?: number;
  content?: number[];
}

export interface TokenDecodedInfo {
  modelName?: string;
  /** @format int32 */
  maxTokenLength?: number;
  data?: string;
}

export interface TypeTokenEncodedInfo {
  success?: boolean;
  data?: TokenEncodedInfo;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalPages?: number;
  /** @format int64 */
  totalCount?: number;
  nullData?: boolean;
}

export interface TokenEncodedInfo {
  modelName?: string;
  /** @format int32 */
  maxTokenLength?: number;
  tokens?: number[];
}

export interface TypeTokenDecodedInfo {
  success?: boolean;
  data?: TokenDecodedInfo;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalPages?: number;
  /** @format int64 */
  totalCount?: number;
  nullData?: boolean;
}

export interface TypeListContentSplitResult {
  success?: boolean;
  data?: ContentSplitResult[];
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalPages?: number;
  /** @format int64 */
  totalCount?: number;
  nullData?: boolean;
}

export interface ContentSplitResult {
  contentList?: string[];
  /** @format int32 */
  startIndex?: number;
  /** @format int32 */
  endIndex?: number;
}

export interface CodeRuleOptimizationAgentResult {
  optimizedRules?: string;
  ogRules?: string;
}

export interface CodeReviewResultRepositoryDTO {
  module?: string;
  /** @format int32 */
  projectId?: number;
  /** @format int64 */
  alterSheetId?: number;
  /** @format int32 */
  mergeRequestId?: number;
  recommendedConclusions?: string;
  codeSpecificationConclusions?: string;
  codeImpactAssessmentConclusions?: string;
  codeSpecificationStatus?: string;
  codeImpactAssessmentStatus?: string;
  codeSummary?: string;
  codeChangeType?: string;
  codeIfAddUnitTest?: string;
  /** @format byte */
  codeReviewExpectedTime?: string;
  reviewLastUpdatedTime?: string;
  /** @format int32 */
  isSuccess?: number;
  failReason?: string;
  codeSpecificationResult?: string;
  /** @format date-time */
  gmtCreateTime?: string;
  /** @format date-time */
  gmtUpdateTime?: string;
  /** @format int64 */
  id?: number;
}

export interface SpecificationExampleConfig {
  modelId?: string;
  specificationItemContent?: string;
  /** @format int32 */
  exampleCount?: number;
  promptExecutionSettings?: PromptExecutionSettings;
  extra?: Record<string, object>;
}

export interface SpecificationExample {
  satisfiedCode?: string;
  satisfiedCodeDescription?: string;
  unsatisfiedCode?: string;
  unsatisfiedCodeDescription?: string;
}

export interface CodeReviewRequest {
  /** @format int32 */
  mergeRequestId?: number;
  repoPath?: string;
  enableInsertComment?: boolean;
  from?: string;
  to?: string;
}

export interface CodeFeedbackItemVO {
  /** @format int32 */
  noteId?: number;
  status?: string;
  rejectReason?: string;
  relevantFile?: string;
  relevantRules?: string;
  language?: string;
  suggestion?: string;
  suggestionLevel?: string;
  relevantLine?: string;
  relevantLineNo?: string;
}

export interface CodeReviewInformationResponse {
  /** @format int32 */
  isSuccess?: number;
  failReason?: string;
  /** @format int64 */
  recordId?: number;
  reviewLastUpdatedTime?: string;
  codeReviewSummaryResultList?: CodeReviewSummaryFeedbackItemVO[];
  codeReviewAgentResultsMap?: Record<string, CodeReviewResultVO>;
}

export interface CodeReviewResultVO {
  codeFeedbackList?: CodeFeedbackItemVO[];
  ip?: string;
}

export interface CodeReviewSummaryFeedbackItemVO {
  codeReviewSubAgentName?: string;
  codeReviewResult?: string;
}

export interface CodeDiffDiagramRequest {
  /** @format int64 */
  sceneId?: number;
  /** @format int32 */
  projectId?: number;
  repo?: string;
  from?: string;
  to?: string;
  /** @format int64 */
  moduleId?: number;
  /** @format int64 */
  spaceId?: number;
  /** @format int64 */
  appId?: number;
  /** @format int64 */
  moduleTeamSpaceId?: number;
  /** @format int64 */
  moduleBizSpaceId?: number;
}

export interface CompletionRequest {
  model?: string;
  prompt?: string;
  stream?: boolean;
  /** @format double */
  temperature?: number;
  /** @format int32 */
  n?: number;
  stop?: object;
  /** @format int32 */
  max_tokens?: number;
  /** @format int32 */
  max_new_tokens?: number;
  /** @format double */
  top_p?: number;
  /** @format int32 */
  top_k?: number;
  /** @format double */
  presence_penalty?: number;
  /** @format double */
  frequency_penalty?: number;
  /** @format double */
  repitition_penalty?: number;
  logit_bias?: Record<string, object>;
}

export interface SseEmitter {
  /** @format int64 */
  timeout?: number;
}

export interface ChatFunction {
  name?: string;
  parameters?: Record<string, object>;
  description?: string;
}

export interface ChatMessage {
  role?: string;
  content?: object;
  name?: string;
}

export interface ChatRequest {
  model?: string;
  /** @format int64 */
  timeoutMs?: number;
  /** @format int64 */
  perEventTimeoutMs?: number;
  messages?: ChatMessage[];
  functions?: ChatFunction[];
  dashScopeApiToken?: string;
  stream?: boolean;
  /** @format double */
  temperature?: number;
  /** @format int32 */
  n?: number;
  stop?: object;
  user?: string;
  returnFinish?: boolean;
  function_call?: object;
  /** @format int32 */
  max_tokens?: number;
  /** @format int32 */
  max_new_tokens?: number;
  /** @format double */
  top_p?: number;
  /** @format int32 */
  top_k?: number;
  /** @format double */
  presence_penalty?: number;
  /** @format double */
  frequency_penalty?: number;
  /** @format double */
  repitition_penalty?: number;
  logit_bias?: Record<string, object>;
  extend_fields?: Record<string, object>;
}

export interface ChatChoice {
  /** @format int32 */
  index?: number;
  delta?: ChatOutput;
  message?: ChatOutput;
  finish_reason?: string;
}

export interface ChatFunctionCall {
  arguments?: string;
  name?: string;
}

export interface ChatOutput {
  role?: string;
  content?: string;
  function_call?: ChatFunctionCall;
}

export interface ChatResponse {
  id?: string;
  object?: string;
  /** @format int32 */
  created?: number;
  model?: string;
  choices?: ChatChoice[];
  usage?: ChatUsage;
  message?: string;
  extend_fields?: Record<string, object>;
  system_fingerprint?: string;
  /** @format int32 */
  error_code?: number;
}

export interface ChatUsage {
  /** @format int32 */
  prompt_tokens?: number;
  /** @format int32 */
  completion_tokens?: number;
  /** @format int32 */
  total_tokens?: number;
}

export type ServerSentEventString = object;

export interface OpenAIEmbeddingRequest {
  model?: string;
  input?: object;
}

export interface EmbeddingData {
  object?: string;
  /** @format int32 */
  index?: number;
  embedding?: number[];
}

export interface OpenAIEmbeddingResult {
  object?: string;
  model?: string;
  data?: EmbeddingData[];
}

export interface ChatDTO {
  prompt?: string;
  promptExecutionSettings?: PromptExecutionSettingsDTO;
}

export interface ChatResponseVO {
  content?: string;
}

export interface SlimmingRequestDTO {
  module?: string;
  moduleName?: string;
  repo?: string;
  from?: string;
  to?: string;
  codeDir?: string;
  /** @uniqueItems true */
  strategies?: ('ADDED_METHODS' | 'MODIFIED_METHODS' | 'OTHER_METHODS' | 'UNCALLED_METHODS' | 'UNREACHED_METHODS')[];
  recalculate?: boolean;
  empId?: string;
  /** @format int64 */
  messageId?: number;
  /** @format int64 */
  appId?: number;
  appVersion?: string;
  moduleDepKey?: string;
  moduleGroupId?: string;
  moduleArtifactId?: string;
  moduleType?: string;
}

export interface CommitBlame {
  authorName?: string;
  commitId?: string;
  commitShortId?: string;
  /** @format date-time */
  createdAt?: string;
  title?: string;
  /** @format int32 */
  start?: number;
  /** @format int32 */
  end?: number;
}

export interface SlimmingClassDTO {
  repo?: string;
  tag?: string;
  path?: string;
  url?: string;
  language?: string;
  /** @format int32 */
  rows?: number;
  className?: string;
  classSourceCode?: string;
  /** @format int32 */
  startRow?: number;
  /** @format int32 */
  endRow?: number;
  blames?: CommitBlame[];
}

export interface SlimmingMethodDTO {
  repo?: string;
  tag?: string;
  path?: string;
  url?: string;
  language?: string;
  methodName?: string;
  methodParameters?: string;
  methodDocstring?: string;
  methodSourceCode?: string;
  /** @format double */
  similarity?: number;
  category?: string;
  className?: string;
  /** @format int32 */
  startRow?: number;
  /** @format int32 */
  endRow?: number;
  /** @format int32 */
  rows?: number;
  blames?: CommitBlame[];
}

export interface SlimmingResultDTO {
  documentId?: string;
  slimmingMethods?: SlimmingMethodDTO[];
  slimmingClass?: SlimmingClassDTO;
  /** @format int32 */
  preByteCount?: number;
  /** @format int32 */
  postByteCount?: number;
  /** @format int32 */
  rawDiffByteCount?: number;
  /** @format int32 */
  diffByteCount?: number;
  slimmingResult?: string;
  strategy?: 'ADDED_METHODS' | 'MODIFIED_METHODS' | 'OTHER_METHODS' | 'UNCALLED_METHODS' | 'UNREACHED_METHODS';
  /** @format double */
  similarity?: number;
  /** @format int64 */
  messageId?: number;
  slimmingReason?: string;
  feedbackStatus?: 'INIT' | 'ACCEPTED' | 'REJECTED' | 'COMPLETED';
  slimmingUniqueSymbol?: string;
  slimmingUniqueSymbols?: string[];
  feedbackDetail?: SlimmingResultFeedbackDetailDTO;
  valid?: boolean;
}

export interface SlimmingResultFeedbackDetailDTO {
  operator?: string;
  /** @format date-time */
  operateTime?: string;
  rejectReason?: string;
  /** @format int64 */
  originRecordId?: number;
}

export interface SlimmingResultSummaryDTO {
  results?: SlimmingResultDTO[];
  /** @format int64 */
  messageId?: number;
  message?: string;
  isTemp?: boolean;
}

export interface TypeSlimmingResultSummaryDTO {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: SlimmingResultSummaryDTO;
  ip?: string;
}

export interface TypeSlimmingResultDTO {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: SlimmingResultDTO;
  ip?: string;
}

export interface TypeListCodeMethod {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: CodeMethod[];
  ip?: string;
}

export interface CodeFile {
  packageName?: string;
  fileName?: string;
  fileSourceCode?: string;
  module?: string;
  repo?: string;
  ref?: string;
  filePath?: string;
  language?: string;
  /** @format int32 */
  startRow?: number;
  /** @format int32 */
  endRow?: number;
}

export interface TypeListCodeFile {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: CodeFile[];
  ip?: string;
}

export interface TypeListCodeClass {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: CodeClass[];
  ip?: string;
}

export interface CoderCollectTag {
  /** @format int64 */
  appId?: number;
  module?: string;
  repo?: string;
  tag?: string;
  codeDir?: string;
}

export interface TypeListDocumentItem {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: DocumentItem[];
  ip?: string;
}

export interface CodeSpecReviewCustomRules {
  customRules?: CodeSpecReviewOneCustomRule[];
}

export interface CodeSpecReviewOneCustomRule {
  ruleId?: string;
  ruleMatchId?: string;
  ruleName?: string;
  ruleDesc?: string;
  ruleTypes?: string[];
  ruleLevel?: string;
  ruleLanguages?: string[];
  /** @format int64 */
  ruleAppId?: number;
  /** @format int64 */
  ruleModuleId?: number;
  /** @format int64 */
  ruleSpaceId?: number;
  ruleStatus?: string;
  ruleEngine?: string;
}

export interface TypeCodeSpecReviewCustomRules {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: CodeSpecReviewCustomRules;
  ip?: string;
}

export interface TypeListSlimmingResultDTO {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: SlimmingResultDTO[];
  ip?: string;
}

export interface TemporaryAgentChatRequestDTO {
  empId?: string;
  /** @format int64 */
  sessionId?: number;
  ssoTicket?: string;
  questions?: ChatMessage[];
  variableMap?: Record<string, object>;
  stream?: boolean;
  /** @format int32 */
  contextHistoryInteractionCount?: number;
  agentInstanceConfig?: AgentInstanceBO;
}

export interface AgentSessionQuery {
  empId?: string;
  chatType?: 'AIMI' | 'OTHER';
  platform?: 'IDE' | 'WEB' | 'API';
  agentIdentifier?: string;
}

export interface AgentSessionChatDetail {
  summary?: string;
  agentIdentifier?: string;
}

export interface AgentSessionVO {
  /** @format int64 */
  id?: number;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtUpdated?: string;
  creator?: string;
  platform?: string;
  chatType?: string;
  chatDetail?: AgentSessionChatDetail;
  /** @format int32 */
  interactionCount?: number;
  messages?: ChatMessage[];
  interactions?: ChatMessage[];
}

export interface TypeListAgentSessionVO {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: AgentSessionVO[];
  ip?: string;
}

export interface AgentSessionCreateDTO {
  empId?: string;
  chatType?: 'AIMI' | 'OTHER';
  platform?: 'IDE' | 'WEB' | 'API';
  summary?: string;
  agentIdentifier?: string;
}

export interface TypeLong {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  /** @format int64 */
  data?: number;
  ip?: string;
}

export interface AgentChatRequestDTO {
  empId?: string;
  /** @format int64 */
  sessionId?: number;
  ssoTicket?: string;
  questions?: ChatMessage[];
  variableMap?: Record<string, object>;
  stream?: boolean;
  /** @format int32 */
  contextHistoryInteractionCount?: number;
}

export interface AgentEditFileDTO {
  originalCode?: string;
  goal?: string;
}

export interface TypeString {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: string;
  ip?: string;
}

export interface TypeLong {
  success?: boolean;
  /** @format int64 */
  data?: number;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalPages?: number;
  /** @format int64 */
  totalCount?: number;
  nullData?: boolean;
}

export interface AiConfigurationBO {
  /** @format int64 */
  id?: number;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  creator?: string;
  modifier?: string;
  entityType?: 'TEAM_SPACE' | 'BIZ_SPACE' | 'MODEL';
  /** @format int64 */
  entityId?: number;
  configType?: 'AI_CR_BLACK_LIST_CONF';
  configAttributeName?: 'AI_CR_INSERT_COMMENT_BLACKLIST';
  configAttributeValue?: string;
  valid?: boolean;
}

export interface AgentTaskRequestObject {
  scene?: string;
  /** @format int64 */
  sceneEntityId?: number;
  params?: object;
  bizEntityDiscernConfig?: BizEntityDiscernConfig;
}

export interface TypeAgentsInteractionsBO {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: AgentsInteractionsBO;
  ip?: string;
}

export interface TypeObject {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: object;
  ip?: string;
}

export interface TextBizIdentity {
  text?: string;
  entityDiscernConfig?: BizEntityDiscernConfig;
}

export interface AgentInstanceDTO {
  type?: string;
  name?: string;
  identifier?: string;
  groupIdentifier?: string;
  description?: string;
  status?: 'ONLINE' | 'OFFLINE';
  agentConfig?:
    | BaseAgentConfig
    | AssistantAgentConfig
    | BuildAgentConfig
    | CodeRewriteAgentConfig
    | DynamicAgentConfig
    | ParserAgentConfig
    | RagAgentConfig
    | RagAgentV2Config
    | SequentialFlowAgentConfig
    | SimpleReflectionAgentConfig;
  creator?: string;
  modifier?: string;
}

export interface AgentTaskDTO {
  /** @format int64 */
  id?: number;
  goal?: string;
  status?: 'NOT_STARTED' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'CANCELED';
  sceneType?: string;
  /** @format int64 */
  sceneEntityId?: number;
  planInfo?: string;
  taskConfig?: string;
  finalResult?: string;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  creator?: string;
  modifier?: string;
}

export interface AgentTaskStepDTO {
  /** @format int64 */
  id?: number;
  /** @format int64 */
  agentTaskId?: number;
  /** @format int32 */
  stepIndex?: number;
  thought?: string;
  action?: string;
  actionVariables?: string;
  observation?: string;
  originalResponse?: string;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  creator?: string;
  modifier?: string;
}

export interface DataEvalJobInstanceBO {
  /** @format int64 */
  id?: number;
  /** @format int64 */
  dataEvalJobId?: number;
  /** @format int64 */
  dataSourceId?: number;
  toolRunParams?: Record<string, object>;
  /** @format date-time */
  startTime?: string;
  /** @format date-time */
  endTime?: string;
  /** @format int64 */
  resultSourceId?: number;
  evalStatus?: 'FINISH' | 'RUNNING' | 'ERROR';
  extra?: Record<string, string>;
}

export interface CodeChartDTO {
  /** @format int64 */
  id?: number;
  /** @format int64 */
  appId?: number;
  /** @format int64 */
  projectId?: number;
  chartType?:
    | 'CODE_SEMANTIC_FLOW_CHART_PLANTUML'
    | 'CODE_SEQUENCE_FLOW_CHART_PLANTUML'
    | 'CODE_SEMANTIC_FLOW_CHART_MERMAID';
  identifier?: string;
  relatedScene?: 'CR_CODE_DIFF';
  /** @format int64 */
  relatedSceneId?: number;
  metadata?: Record<string, object>;
  source?: string;
  chartData?: string;
  chartExtraInfo?: Record<string, object>;
  codeSummary?: string;
}

export interface PromptTemplateVO {
  bizIdentifier?: string;
  promptTemplate?: string;
  inputVariables?: string[];
  inputHistory?: string[][];
  matchCondition?: string;
  executionSettings?: string;
  format?: 'SEMANTIC_KERNEL' | 'HANDLEBARS' | 'TEXT';
  /** @format int64 */
  id?: number;
}

export interface TypeListLLMInfo {
  success?: boolean;
  data?: LLMInfo[];
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalPages?: number;
  /** @format int64 */
  totalCount?: number;
  nullData?: boolean;
}

export interface LLMInfo {
  llmEndpoint?: string;
  modelName?: string;
  api?: string;
  /** @format int32 */
  modelId?: number;
  /** @format int32 */
  semaphoreMax?: number;
  /** @format int32 */
  maxTokenLength?: number;
  /** @format double */
  temperature?: number;
  /** @format double */
  topP?: number;
  extra?: Record<string, string>;
  hash?: string;
}

export interface IncrCodeScanResultDO {
  /** @format int64 */
  id?: number;
  /** @format date-time */
  gmtCreate?: string;
  /** @format date-time */
  gmtModified?: string;
  request?: IncrCodeScanRequest;
  codeDiffMethodSet?: CodeDiffMethodSet;
  suggestions?: CodeSuggestionItem[];
  metric?: Metric;
  module?: string;
  repo?: string;
  from?: string;
  to?: string;
}

export interface TypeIncrCodeScanResultDO {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: IncrCodeScanResultDO;
  ip?: string;
}

export interface TypeEvalDataDTO {
  success?: boolean;
  data?: EvalDataDTO;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalPages?: number;
  /** @format int64 */
  totalCount?: number;
  nullData?: boolean;
}

export interface EvalDataDTO {
  /** @format int64 */
  dataSourceId?: number;
  data?: Record<string, Record<string, string>>;
  extra?: Record<string, string>;
}

export interface TypeEvalContentDTO {
  items?: EvalContentDTO[];
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  pages?: number;
  /** @format int64 */
  totalCount?: number;
}

export interface TypeEvalContentDTO {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: any;
  ip?: string;
}

export interface DataEvalJobVO {
  /** @format int64 */
  id?: number;
  evalJobName?: string;
  evalJobDesc?: string;
  triggerCron?: string;
  triggerExtra?: Record<string, string>;
  evalTool?: EvalTool;
  toolRunTemplate?: Record<string, object>;
  jobExtra?: Record<string, string>;
  dataSourceTemplate?: Record<string, string>;
  isDeleted?: boolean;
}

export interface TypeDataEvalJobVO {
  items?: DataEvalJobVO[];
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  pages?: number;
  /** @format int64 */
  totalCount?: number;
}

export interface TypeDataEvalJobVO {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: any;
  ip?: string;
}

export interface TypeDataEvalJobInstanceBO {
  items?: DataEvalJobInstanceBO[];
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  pages?: number;
  /** @format int64 */
  totalCount?: number;
}

export interface TypeDataEvalJobInstanceBO {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: any;
  ip?: string;
}

export interface ResultDataEvalJobInstanceBO {
  success?: boolean;
  data?: DataEvalJobInstanceBO;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
}

export interface EvalDataSourceBO {
  sourceType?: 'LOCAL' | 'MYSQL' | 'ODPS';
  sourceContent?: string;
  metaData?: string;
}

export interface ResultEvalDataSourceBO {
  success?: boolean;
  data?: EvalDataSourceBO;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
}

export interface TypeDocumentItem {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: DocumentItem;
  ip?: string;
}

export interface DependencyGraphSymbol {
  /** @format int64 */
  appId?: number;
  appVersion?: string;
  moduleDepKey?: string;
  moduleVersion?: string;
  symbolType?: string;
  symbolIdentifier?: string;
  /** @format int32 */
  startRow?: number;
  /** @format int32 */
  endRow?: number;
  /** @format int32 */
  rows?: number;
}

export interface TypeListDependencyGraphSymbol {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: DependencyGraphSymbol[];
  ip?: string;
}

export interface Class {
  moduleGav?: string;
  className?: string;
}

export interface TypeListClass {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: Class[];
  ip?: string;
}

export interface DocstringMetaData {
  documentId?: string;
  module?: string;
  repo?: string;
  tag?: string;
  language?: string;
  type?: string;
  path?: string;
  className?: string;
  method?: string;
  parameters?: string;
  docstring?: string;
  /** @format int32 */
  startRow?: number;
  /** @format int32 */
  endRow?: number;
  vector?: number[];
  /** @format double */
  score?: number;
}

export interface CodeBasicInfoObject {
  module?: string;
  repo?: string;
  ref?: string;
  filePath?: string;
  language?: string;
  /** @format int32 */
  startRow?: number;
  /** @format int32 */
  endRow?: number;
}

export interface TypeAgentSessionVO {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: AgentSessionVO;
  ip?: string;
}

export interface TypeListAiConfigurationEntityType {
  success?: boolean;
  data?: ('TEAM_SPACE' | 'BIZ_SPACE' | 'MODEL')[];
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalPages?: number;
  /** @format int64 */
  totalCount?: number;
  nullData?: boolean;
}

export interface TypeListLong {
  success?: boolean;
  data?: number[];
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalPages?: number;
  /** @format int64 */
  totalCount?: number;
  nullData?: boolean;
}

export interface TypeListAiConfigurationConfigType {
  success?: boolean;
  data?: 'AI_CR_BLACK_LIST_CONF'[];
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalPages?: number;
  /** @format int64 */
  totalCount?: number;
  nullData?: boolean;
}

export interface TypeListAiConfigurationConfigAttributeName {
  success?: boolean;
  data?: 'AI_CR_INSERT_COMMENT_BLACKLIST'[];
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  totalPages?: number;
  /** @format int64 */
  totalCount?: number;
  nullData?: boolean;
}

export interface TypeJSONObject {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: {
    empty?: boolean;
    innerMap?: Record<string, object>;
    [key: string]: any;
  };
  ip?: string;
}

export interface AgentInteractionsSearchDTO {
  /** @format int64 */
  sceneEntityId?: number;
  sceneEntityType?: string;
  status?: 'NOT_STARTED' | 'RUNNING' | 'SUCCESS' | 'FAILED' | 'CANCELED';
  thoughtMode?:
    | 'SIMPLE_AGENT'
    | 'SINGLE_ROUND'
    | 'MULTI_ROUND'
    | 'TWO_AGENT'
    | 'ANALYZE_SUMMARIZE_AGENT'
    | 'BUILD_SUMMARIZE_AGENT'
    | 'GROUP_AGENT_AUTO'
    | 'MULTI_CODE_REWRITE_RANK';
  /** @format date-time */
  startTime?: string;
  /** @format date-time */
  endTime?: string;
}

export interface TypeAgentsInteractionsBO {
  items?: AgentsInteractionsBO[];
  /** @format int32 */
  pageNum?: number;
  /** @format int32 */
  pageSize?: number;
  /** @format int32 */
  pages?: number;
  /** @format int64 */
  totalCount?: number;
}

export interface TypeAgentsInteractionsBO {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: any;
  ip?: string;
}

export interface TypeListAgentsInteractionsBO {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: AgentsInteractionsBO[];
  ip?: string;
}

export interface TypeAgentInstanceDTO {
  success?: boolean;
  /** @format int32 */
  errorNo?: number;
  errorCode?: string;
  errorMsg?: string;
  data?: AgentInstanceDTO;
  ip?: string;
}
