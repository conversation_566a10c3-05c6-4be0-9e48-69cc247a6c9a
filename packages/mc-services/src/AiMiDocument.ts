/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { DocumentChunkConfigUpdateRequest, DocumentPageRequest, PageDocumentVO } from './AiMiDataContracts';
import { getBaseUrl, MethodOptions } from './HttpClient';
import { request } from './Request';

const baseUrl = getBaseUrl('https://aimi.alibaba-inc.com');

export interface ModifyDocumentStatusParams {
  /** @format int64 */
  documentId: number;
  /** 文档处理状态 */
  status: 'PROCESSING' | 'ACTIVE' | 'ERROR' | 'REMOVED' | 'DISABLED';
}
/**
 * No description
 * @tags AiMiDocument
 * @name ModifyDocumentStatus
 * @summary 修改文档状态
 * @request PUT:/aimi/api/v1/document/status
 */
export async function modifyDocumentStatus(
  query: ModifyDocumentStatusParams,
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/aimi/api/v1/document/status`, {
    method: 'PUT',
    params: query,
    ...options,
    suffix: false,
  });
}

/**
 * No description
 * @tags AiMiDocument
 * @name UpdateDocumentChunkConfig
 * @summary 更新文档分段配置
 * @request PUT:/aimi/api/v1/document/chunkConfig
 */
export async function updateDocumentChunkConfig(
  data: DocumentChunkConfigUpdateRequest,
  options?: MethodOptions,
): Promise<number> {
  return request(`${baseUrl}/aimi/api/v1/document/chunkConfig`, {
    method: 'PUT',
    body: data as any,
    ...options,
    suffix: false,
  });
}

export interface PageDocumentParams {
  /** 文档列表请求 */
  request: DocumentPageRequest;
}
/**
 * No description
 * @tags AiMiDocument
 * @name PageDocument
 * @summary 分页获取知识库中的文档列表
 * @request GET:/aimi/api/v1/document/page
 */
export async function pageDocument(query: PageDocumentParams, options?: MethodOptions): Promise<PageDocumentVO> {
  return request(`${baseUrl}/aimi/api/v1/document/page`, {
    method: 'GET',
    params: query,
    ...options,
    suffix: false,
  });
}
