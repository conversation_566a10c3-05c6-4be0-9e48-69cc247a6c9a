/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { FusionAppBaseDTO, MiniappOperatorAndAppDTO } from './EnhanceDataContracts';
import { getBaseUrl, MethodOptions, request } from './HttpClient';

const baseUrl = getBaseUrl('https://mc.alibaba-inc.com/oapi');

/**
 * No description
 * @tags EnhanceMiniApp
 * @name CreateAssetsCacheBpmsInstance
 * @request POST:/uniapp/assetsCache/createBpmsInstance
 */
export async function createAssetsCacheBpmsInstance(data: number, options?: MethodOptions): Promise<any> {
  return request(`${baseUrl}/uniapp/assetsCache/createBpmsInstance`, {
    method: 'POST',
    body: data as any,
    ...options,
    suffix: false,
  });
}

export interface FindMiniAppsByUserParams {
  havanaId: string;
}
/**
 * No description
 * @tags EnhanceMiniApp
 * @name FindMiniAppsByUser
 * @request GET:/miniapp/searchAppByUserId
 */
export async function findMiniAppsByUser(
  query: FindMiniAppsByUserParams,
  options?: MethodOptions,
): Promise<MiniappOperatorAndAppDTO[]> {
  return request(`${baseUrl}/miniapp/searchAppByUserId`, {
    method: 'GET',
    params: query,
    ...options,
    suffix: false,
  });
}

export interface GetMiniAppInfoParams {
  appId: string;
}
/**
 * No description
 * @tags EnhanceMiniApp
 * @name GetMiniAppInfo
 * @request GET:/miniapp/getAppBase
 */
export async function getMiniAppInfo(query: GetMiniAppInfoParams, options?: MethodOptions): Promise<FusionAppBaseDTO> {
  return request(`${baseUrl}/miniapp/getAppBase`, {
    method: 'GET',
    params: query,
    ...options,
    suffix: false,
  });
}
