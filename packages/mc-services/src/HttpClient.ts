/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

export type { MethodOptions } from './Request';

const MC_PROD_HOSTNAME = 'mc.alibaba-inc.com';
const AIMI_PROD_HOSTNAME = 'aimi.alibaba-inc.com';

let currentHost: string;
let currentPort: string;
// compat with nodejs env
if (typeof window !== 'undefined') {
  currentHost = window.location.hostname;
  currentPort = window.location.port;
} else {
  currentHost = globalThis.MC_HOSTNAME || MC_PROD_HOSTNAME;
}
const isProd = [MC_PROD_HOSTNAME, AIMI_PROD_HOSTNAME].includes(currentHost);
const isAimi = currentHost.endsWith(AIMI_PROD_HOSTNAME);

export function getBaseUrl(baseUrl: string) {
  if (!baseUrl) {
    return '';
  }

  // if (isProd && baseUrl.startsWith("/")) {
  //   // return baseUrl;
  //   return `https://${host}${baseUrl}`;
  // }
  if (isProd) {
    // return baseUrl;
    if (baseUrl.startsWith('/')) {
      return `https://${isAimi ? AIMI_PROD_HOSTNAME : MC_PROD_HOSTNAME}${baseUrl}`;
    }

    return baseUrl;
  }

  // 预发情况下，需要兼容和处理MC又几套预发域名的问题
  // 请求域名是当前页面的访问域名，说明请求的是 MC 自己的接口，否则请求的是其他的域名接口
  const { protocol, hostname, pathname } = new URL(baseUrl);
  const isMC = [MC_PROD_HOSTNAME, AIMI_PROD_HOSTNAME].includes(hostname);
  return `${protocol}//${isMC ? 'pre-' : ''}${isMC ? `${isAimi ? AIMI_PROD_HOSTNAME : MC_PROD_HOSTNAME}${isAimi ? ':' + currentPort : ''}` : hostname}${pathname.replace(/\/$/, '')}`;
}
